# SakuraPanel
二改樱花内网穿透网页端源代码

## 功能和特性
- 支持多用户
- 支持用户组配置
- 支持每个用户单独限速
- 支持每个用户单独限制流量
- 可配置签到获得流量
- 可配置凭邀请码注册账号
- 实时流量统计
- 美观的界面

## 项目结构详细说明

### 根目录文件
| 文件名 | 用途说明 |
|--------|----------|
| `index.php` | 主入口文件，负责系统初始化和路由分发 |
| `configuration.php` | 核心配置文件，包含数据库、邮件、注册等所有配置项 |
| `daemon.php` | 命令行守护进程，用于流量统计收集和节点监控 |
| `gencode.php` | 代码生成工具（如有） |
| `import.sql` | 数据库结构文件，包含所有表结构和初始数据 |

### 核心类库目录 (`core/`)
| 文件名 | 功能说明 |
|--------|----------|
| `Database.php` | 数据库操作封装类，提供查询、插入、更新、删除等方法 |
| `UserManager.php` | 用户管理类，处理登录、注册、权限验证等用户相关操作 |
| `ProxyManager.php` | 隧道管理类，管理TCP/UDP/HTTP/HTTPS等各种类型隧道 |
| `NodeManager.php` | 节点管理类，管理服务器节点和节点权限分配 |
| `Router.php` | 路由控制器，根据GET参数分发到对应页面模块 |
| `PostHandler.php` | POST请求处理器，处理表单提交和AJAX请求 |
| `Pages.php` | 页面渲染类，负责加载和渲染页面模板 |
| `Utils.php` | 工具函数类，提供各种通用工具方法 |
| `Settings.php` | 系统设置类，管理系统配置项 |
| `Smtp.php` | 邮件发送类，封装邮件发送功能 |
| `Regex.php` | 正则表达式验证类，提供各种数据格式验证 |
| `Parsedown.php` | Markdown解析器，用于渲染Markdown内容 |
| `PHPMailer/` | PHPMailer邮件库目录 |

### 页面模块目录 (`modules/`)
| 文件名 | 页面功能 |
|--------|----------|
| `home.php` | 首页仪表板，显示用户概览信息和系统状态 |
| `proxies.php` | 隧道列表页面，显示和管理用户的所有隧道 |
| `addproxy.php` | 添加隧道页面，创建新的内网穿透隧道 |
| `nodes.php` | 节点管理页面，管理服务器节点（管理员功能） |
| `traffic.php` | 流量统计页面，查看流量使用情况和历史记录 |
| `profile.php` | 用户资料页面，修改个人信息和密码 |
| `settings.php` | 系统设置页面，管理系统配置（管理员功能） |
| `userlist.php` | 用户列表页面，管理所有用户（管理员功能） |
| `sign.php` | 签到页面，用户每日签到获取流量 |
| `download.php` | 下载页面，提供客户端下载和配置文件 |
| `configuration.php` | 配置管理页面，系统配置界面 |
| `404.php` | 404错误页面 |

### 页面模板目录 (`pages/`)
| 文件名 | 模板用途 |
|--------|----------|
| `login.php` | 登录页面模板 |
| `register.php` | 注册页面模板 |
| `panel.php` | 主面板框架模板，包含导航栏和侧边栏 |
| `home.php` | 首页模板 |
| `findpass.php` | 找回密码页面模板 |
| `logout.php` | 退出登录处理页面 |
| `403.php` | 403权限错误页面 |
| `404.php` | 404页面未找到模板 |

### API接口目录 (`api/`)
| 文件名 | 接口功能 |
|--------|----------|
| `index.php` | API主入口，提供与FRP服务端通信的接口 |

**主要API端点：**
- `status` - API状态检查
- `getconf` - 获取用户隧道配置文件
- `checktoken` - 验证用户认证令牌
- `checkproxy` - 验证隧道配置合法性
- `getlimit` - 获取用户流量限制信息

### 静态资源目录 (`assets/`)
| 子目录 | 内容说明 |
|--------|----------|
| `configuration/` | 配置相关的静态文件 |
| `download/` | 客户端下载文件存放目录 |
| `email/` | 邮件模板文件 |
| `home/` | 首页相关静态资源 |
| `panel/` | 面板界面的CSS、JS、图片等静态资源 |


## 数据库结构

### 主要数据表
| 表名 | 用途说明 |
|------|----------|
| `users` | 用户基本信息表，存储用户名、密码、邮箱、用户组等 |
| `groups` | 用户组表，定义不同用户组的权限和配额 |
| `nodes` | 服务器节点表，存储FRP服务器节点信息 |
| `proxies` | 隧道配置表，存储所有用户创建的隧道信息 |
| `tokens` | 用户认证令牌表，用于API认证 |
| `limits` | 用户限速配置表，个性化限速设置 |
| `traffic` | 流量统计表，记录用户流量使用情况 |
| `findpass` | 找回密码临时表，存储密码重置链接 |

### 默认用户组
- `default` - 默认组：1GB流量，5个隧道，1Mbps限速
- `vip1` - 青铜VIP：10GB流量，10个隧道，2Mbps限速
- `vip2` - 黄金VIP：20GB流量，15个隧道，3Mbps限速
- `vip3` - 钻石VIP：40GB流量，20个隧道，4Mbps限速

## 系统架构

### 技术栈
- **后端语言**: PHP 7.0+
- **数据库**: MySQL 5.7+ (utf8mb4编码)
- **前端框架**: Bootstrap + jQuery
- **邮件库**: PHPMailer
- **Markdown解析**: Parsedown

### 架构特点
- **MVC模式**: 模型-视图-控制器分离
- **面向对象**: 核心功能封装为类
- **模块化设计**: 页面功能独立模块
- **API接口**: RESTful风格的API设计
- **安全机制**: CSRF防护、SQL注入防护、权限控制

### 支持的隧道类型
- **TCP隧道**: 支持任意TCP协议的内网穿透
- **UDP隧道**: 支持UDP协议的内网穿透
- **HTTP隧道**: 支持HTTP网站的内网穿透
- **HTTPS隧道**: 支持HTTPS网站的内网穿透
- **STCP隧道**: 安全的TCP隧道，需要密钥
- **XTCP隧道**: P2P模式的TCP隧道

## 安装配置

### 环境要求
- PHP 7.0+ (推荐7.4+)
- MySQL 5.7+ 或 MariaDB 10.2+
- Web服务器 (Apache/Nginx)
- 配套的SakuraFrp服务端

### 安装步骤
1. 将项目文件上传到Web目录
2. 创建MySQL数据库，编码设置为 `utf8mb4_unicode_ci`
3. 导入 `import.sql` 文件到数据库
4. 修改 `configuration.php` 配置数据库连接信息
5. 修改 `api/index.php` 配置API Token
6. 修改 `daemon.php` 配置数据库连接（如需使用守护进程）
7. 设置Web目录权限，确保PHP可读写
8. 访问网站注册管理员账号
9. 在数据库中将管理员账号的 `group` 字段设置为 `admin`

### 重要配置文件
| 文件名 | 配置内容 |
|--------|----------|
| `configuration.php` | 数据库、邮件、注册、签到等核心配置 |
| `api/index.php` | API Token配置，需与FRP服务端一致 |
| `daemon.php` | 守护进程数据库配置 |

## 功能模块详解

### 用户管理系统
- **用户注册**: 支持邮箱验证、邀请码注册
- **用户登录**: 用户名/邮箱登录，密码加密存储
- **权限管理**: 基于用户组的权限控制系统
- **流量管理**: 每日流量统计和配额限制
- **签到系统**: 每日签到获取随机流量奖励

### 隧道管理系统
- **隧道创建**: 支持多种协议类型的隧道创建
- **端口分配**: 自动分配可用端口，支持端口保留
- **域名绑定**: HTTP/HTTPS隧道支持自定义域名
- **状态监控**: 实时显示隧道连接状态
- **批量操作**: 支持批量启用/禁用隧道

### 节点管理系统
- **多节点支持**: 支持多个FRP服务器节点
- **负载均衡**: 用户可选择不同节点创建隧道
- **权限分配**: 不同用户组可访问不同节点
- **状态监控**: 实时监控节点在线状态

### 流量统计系统
- **实时统计**: 实时显示当前流量使用情况
- **历史记录**: 保存历史流量使用记录
- **图表展示**: 可视化流量使用趋势
- **限制告警**: 流量超限自动告警

### 管理员功能
- **用户管理**: 查看、编辑、删除用户
- **节点管理**: 添加、配置、监控服务器节点
- **系统设置**: 修改系统配置参数
- **数据统计**: 查看系统整体使用情况

## API接口说明

### 认证方式
API使用Token认证，格式为：`API_TOKEN|NODE_ID`

### 主要接口
```
GET /api/?action=status
- 功能: 检查API状态
- 返回: JSON格式的状态信息

GET /api/?action=getconf&token={USER_TOKEN}&node={NODE_ID}
- 功能: 获取用户的FRP配置文件
- 参数: token(用户令牌), node(节点ID)
- 返回: FRP配置文件内容

GET /api/?action=checktoken&user={USER_TOKEN}&apitoken={API_TOKEN}
- 功能: 验证用户令牌有效性
- 参数: user(用户令牌), apitoken(API令牌)
- 返回: 验证结果

GET /api/?action=checkproxy&user={USER_TOKEN}&proxy_name={PROXY_NAME}&...
- 功能: 验证隧道配置合法性
- 参数: 根据隧道类型不同而不同
- 返回: 验证结果

GET /api/?action=getlimit&user={USER_TOKEN}&apitoken={API_TOKEN}
- 功能: 获取用户流量限制
- 参数: user(用户令牌), apitoken(API令牌)
- 返回: 流量限制信息
```

## 配套 Frps 服务端
本面板需要专用 Frps 才能兼容，请访问我的另一个项目：https://github.com/ZeroDream-CN/SakuraFrp

请按照另一个项目的介绍在每个服务器节点上进行配置。

Frpc 客户端无特殊需求，只要版本是 0.28.0 都可以兼容使用。

## 守护进程说明

`daemon.php` 是一个命令行守护进程，主要功能：
- 定期收集各节点的流量统计数据
- 监控节点在线状态
- 清理过期的临时数据
- 发送系统通知邮件

### 运行方式
```bash
# 在命令行中运行
php daemon.php

# 或者设置为系统服务自动运行
# 具体方法根据操作系统而定
```

## 安全建议

### 服务器安全
- 定期更新PHP和MySQL版本
- 配置防火墙，只开放必要端口
- 使用HTTPS加密Web访问
- 定期备份数据库

### 应用安全
- 修改默认的API Token
- 设置强密码策略
- 启用邮箱验证
- 定期检查用户权限

### 数据安全
- 定期备份数据库
- 监控异常流量
- 记录操作日志
- 设置流量告警

## 常见问题

### Q: 如何设置管理员账号？
A: 注册账号后，在数据库 `users` 表中将对应用户的 `group` 字段设置为 `admin`。

### Q: 忘记管理员密码怎么办？
A: 可以直接在数据库中修改密码字段，或使用找回密码功能。

### Q: 如何添加新的服务器节点？
A: 登录管理员账号，在节点管理页面添加新节点，然后在对应服务器上部署SakuraFrp服务端。

### Q: 用户无法创建隧道？
A: 检查用户组配额、节点权限、端口范围等配置是否正确。

### Q: API接口返回错误？
A: 检查API Token配置是否与FRP服务端一致，确认节点状态正常。

### Q: 流量统计不准确？
A: 确保守护进程正常运行，检查节点API连接是否正常。

## 更新日志

### v1.0.2
- 优化API接口性能
- 修复流量统计bug
- 增加更多隧道类型支持
- 改进用户界面

### v1.0.1
- 修复安全漏洞
- 优化数据库查询
- 增加邮件通知功能

### v1.0.0
- 初始版本发布
- 基础功能实现

## 开发说明

### 目录结构规范
- `core/` - 核心类库，不要直接修改
- `modules/` - 页面模块，可根据需要扩展
- `pages/` - 页面模板，可自定义样式
- `assets/` - 静态资源，CSS/JS/图片等
- `api/` - API接口，谨慎修改

### 代码规范
- 使用PSR-4自动加载规范
- 类名使用大驼峰命名
- 方法名使用小驼峰命名
- 常量使用全大写下划线命名

### 扩展开发
如需扩展功能，建议：
1. 在 `modules/` 目录添加新的页面模块
2. 在 `core/` 目录添加新的功能类
3. 修改 `Router.php` 添加新的路由
4. 更新数据库结构（如需要）

## 许可协议
本软件仅限个人非盈利使用，完整许可协议请查看 LICENSE 文件。

如需用于商业行为（例如出租内网穿透），或者需要技术支持，请与开发者联系。

## 贡献指南
欢迎提交Issue和Pull Request来改进项目：
1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 技术支持
- 提交Issue: 在GitHub项目页面提交问题
- 邮件联系: 通过项目页面联系开发者
- 社区讨论: 加入相关技术群组讨论

---

## 📚 详细文档

更多详细文档请查看 [docs 文档目录](docs/):

### 核心文档
- [📖 文档中心首页](docs/README.md) - 完整的文档导航
- [🚀 安装配置指南](docs/installation.md) - 详细的部署步骤
- [📡 API接口文档](docs/api.md) - 完整的API说明
- [🗄️ 数据库结构](docs/database.md) - 数据表结构详解

### 功能文档
- [🔍 节点状态检测功能](docs/节点状态检测功能说明.md) - 节点监控功能详解
- [🛠️ 故障排除指南](docs/troubleshooting.md) - 常见问题解决方案

### 快速链接
- 新用户建议先阅读：[安装配置指南](docs/installation.md)
- 开发者建议查看：[API接口文档](docs/api.md)
- 遇到问题请参考：[故障排除指南](docs/troubleshooting.md)

**注意**: 本项目为开源项目，使用前请仔细阅读许可协议。在生产环境中使用时，请确保做好安全防护和数据备份。