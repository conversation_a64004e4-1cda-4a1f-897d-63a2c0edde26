<?php
namespace SakuraPanel;

use SakuraPanel;

// 测试节点状态检测功能
define("ROOT", __DIR__);

include(ROOT . "/configuration.php");
include(ROOT . "/core/Database.php");
include(ROOT . "/core/Regex.php");
include(ROOT . "/core/Utils.php");

$conn = null;
$db = new SakuraPanel\Database();

include(ROOT . "/core/NodeManager.php");

$nm = new SakuraPanel\NodeManager();

echo "<h1>节点状态检测测试</h1>";

// 获取所有节点
$nodes = Database::toArray(Database::query("nodes", []));

if (empty($nodes)) {
    echo "<p>没有找到任何节点</p>";
    exit;
}

echo "<h2>单个节点检测测试</h2>";
foreach ($nodes as $node) {
    echo "<h3>节点 {$node['id']}: {$node['name']}</h3>";
    echo "<p>IP: {$node['ip']}, 管理端口: {$node['admin_port']}</p>";
    
    $result = $nm->checkNodeStatus($node['id']);
    
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    echo "<hr>";
}

echo "<h2>批量节点检测测试</h2>";
$allResults = $nm->checkAllNodesStatus();

echo "<pre>";
print_r($allResults);
echo "</pre>";

echo "<h2>状态文本和CSS类测试</h2>";
$statusCodes = [200, 401, 403, 500, 404];
foreach ($statusCodes as $code) {
    $text = $nm->getStatusText($code);
    $class = $nm->getStatusClass($code);
    echo "<p>状态码 {$code}: <span class='{$class}'>{$text}</span></p>";
}
?>
