# SakuraPanel 测试文件

本目录包含了 SakuraPanel 的测试文件和调试工具。

## 📁 文件说明

### test_node_status.php
**功能**: 节点状态检测功能测试

**用途**:
- 测试节点连接状态
- 调试节点检测功能
- 验证API响应

**使用方法**:
```bash
# 在命令行中运行
php tests/test_node_status.php

# 或在浏览器中访问
http://your-domain.com/tests/test_node_status.php
```

**输出内容**:
- 单个节点检测结果
- 批量节点检测结果
- 状态文本和CSS类测试

## 🧪 测试指南

### 运行测试前的准备
1. 确保数据库连接正常
2. 确保至少有一个节点配置
3. 确保FRP服务端正常运行

### 测试步骤
1. **基础连接测试**:
   ```bash
   php tests/test_node_status.php
   ```

2. **检查输出结果**:
   - 查看节点连接状态
   - 确认响应时间合理
   - 验证状态码正确

3. **问题排查**:
   - 如果连接失败，检查网络连接
   - 如果认证失败，检查admin_pass配置
   - 如果超时，检查防火墙设置

### 常见测试结果

#### 正常结果示例
```
节点 1: 测试节点
IP: *************, 管理端口: 7400
Array
(
    [status] => online
    [code] => 200
    [message] => 节点在线
    [response_time] => 45
    [server_info] => Array
        (
            [version] => 0.44.0
            [status] => running
        )
)
```

#### 异常结果示例
```
节点 1: 测试节点
IP: *************, 管理端口: 7400
Array
(
    [status] => offline
    [code] => 500
    [message] => 节点离线或连接失败
    [response_time] => 5000
)
```

## 🔧 调试技巧

### 1. 启用详细错误信息
在测试文件中添加：
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### 2. 检查网络连接
```bash
# 测试端口连通性
telnet node_ip admin_port

# 测试HTTP连接
curl -u admin:password http://node_ip:admin_port/api/status
```

### 3. 查看详细日志
```bash
# 查看PHP错误日志
tail -f /var/log/php/error.log

# 查看Web服务器日志
tail -f /var/log/apache2/error.log
```

## 📝 添加新测试

### 创建新测试文件
1. 复制现有测试文件作为模板
2. 修改测试逻辑
3. 添加适当的错误处理
4. 更新本文档

### 测试文件规范
- 文件名以 `test_` 开头
- 包含详细的输出信息
- 提供清晰的错误提示
- 支持命令行和Web访问

## 🚨 注意事项

### 安全考虑
- 测试文件包含敏感信息，生产环境中应删除或限制访问
- 不要在测试文件中硬编码密码
- 测试完成后及时清理临时数据

### 性能考虑
- 避免在生产环境中频繁运行测试
- 大量测试可能影响系统性能
- 设置合理的超时时间

---

**相关文档**:
- [节点状态检测功能说明](../docs/节点状态检测功能说明.md)
- [故障排除指南](../docs/troubleshooting.md)
