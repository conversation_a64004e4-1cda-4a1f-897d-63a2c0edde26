{"_args": [["icheck-bootstrap@3.0.1", "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE"]], "_from": "icheck-bootstrap@3.0.1", "_id": "icheck-bootstrap@3.0.1", "_inBundle": false, "_integrity": "sha512-Rj3SybdcMcayhsP4IJ+hmCNgCKclaFcs/5zwCuLXH1WMo468NegjhZVxbSNKhEjJjnwc4gKETogUmPYSQ9lEZQ==", "_location": "/icheck-bootstrap", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "icheck-bootstrap@3.0.1", "name": "icheck-bootstrap", "escapedName": "icheck-bootstrap", "rawSpec": "3.0.1", "saveSpec": null, "fetchSpec": "3.0.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/icheck-bootstrap/-/icheck-bootstrap-3.0.1.tgz", "_spec": "3.0.1", "_where": "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/bantikyan/icheck-bootstrap/issues"}, "description": "Pure css checkboxes and radio buttons for Twitter Bootstrap.", "files": ["icheck-bootstrap.css", "icheck-bootstrap.min.css"], "homepage": "https://github.com/bantikyan/icheck-bootstrap#readme", "keywords": ["checkbox", "radio", "bootstrap", "pure", "css"], "license": "MIT", "main": "icheck-bootstrap.css", "name": "icheck-bootstrap", "repository": {"type": "git", "url": "git+https://github.com/bantikyan/icheck-bootstrap.git"}, "version": "3.0.1"}