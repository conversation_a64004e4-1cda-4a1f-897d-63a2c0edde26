{"_args": [["ion-rangeslider@2.3.0", "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE"]], "_from": "ion-rangeslider@2.3.0", "_id": "ion-rangeslider@2.3.0", "_inBundle": false, "_integrity": "sha512-7TtH9/X4Aq/xCzboWxjwlv20gVqR90Ysc3aehMlTuH2/ULaSxpB80hq+yvD1N0FwWbPDtxQpjQrz/iX+LWXKmg==", "_location": "/ion-rangeslider", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ion-rangeslider@2.3.0", "name": "ion-rangeslider", "escapedName": "ion-rangeslider", "rawSpec": "2.3.0", "saveSpec": null, "fetchSpec": "2.3.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/ion-rangeslider/-/ion-rangeslider-2.3.0.tgz", "_spec": "2.3.0", "_where": "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "IonDen"}, "bugs": {"url": "https://github.com/IonDen/ion.rangeSlider/issues"}, "dependencies": {"jquery": ">=1.8"}, "description": "Cool, comfortable and easily customizable range slider with many options and skin support", "directories": {"lib": "js"}, "homepage": "http://ionden.com/a/plugins/ion.rangeSlider/en.html", "ignore": [".idea", "PSD", "bower.json"], "keywords": ["jquery-plugin", "ecosystem:jquery", "j<PERSON>y", "form", "input", "range", "slider", "rangeslider", "interface", "diapason", "ui", "noui", "skins"], "license": "MIT", "main": "./js/ion.rangeSlider.js", "name": "ion-rangeslider", "repository": {"type": "git", "url": "git://github.com/IonDen/ion.rangeSlider.git"}, "version": "2.3.0"}