# Contributing to Ion.RangeSlider project

### Code style

1. Project is using 4 space indentation
2. Function and metod names should be written in camelCase
3. Variables name should be written in lower_case
4. New methods should have JSDoc descriptions

### Guide for Pull Requests with bug fixes

1. Only 1 bugfix per Pull Request
2. Should have bug description
3. Should have bug screenshots (if possible)
4. Should have working demo. Use JSFIDDLE: https://jsfiddle.net/IonDen/b79q0vnm/

### Guide for Pull Requests with new features

1. Only 1 feature per Pull Request
2. Should have statement, why feature is important and should be included in to plugin
3. Should have feature description
4. Should have feature screenshots (if possible)
5. Should have working demo. Use JSFIDDLE: https://jsfiddle.net/IonDen/b79q0vnm/

### Guide for Pull Requests with grammar fixes

1. Just create a pull request :)
