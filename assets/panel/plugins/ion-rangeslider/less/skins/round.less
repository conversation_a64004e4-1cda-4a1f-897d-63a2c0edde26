// Skin design by <PERSON><PERSON><PERSON><PERSON>
// https://github.com/grimals<PERSON>

.irs--round {
    @name: irs;

    @top: 36px;
    @bottom: 21px;
    @line_height: 4px;
    @handle_width: 24px;
    @handle_height: 24px;

    @line_color: #dee4ec;
    @bar_color: #006cfa;
    @handle_color_1: #006cfa;
    @handle_color_2: white;
    @handle_color_3: #f0f6ff;
    @minmax_text_color: #333;
    @minmax_bg_color: rgba(0,0,0,0.1);
    @label_color_1: #006cfa;
    @label_color_2: white;
    @grid_color_1: #dedede;
    @grid_color_2: silver;



    height: 50px;

    &.irs-with-grid {
        height: 65px;
    }

    .@{name}-line {
        top: @top;
        height: @line_height;
        background-color: @line_color;
        border-radius: @line_height;
    }

    .@{name}-bar {
        top: @top;
        height: @line_height;
        background-color: @bar_color;

        &--single {
            border-radius: @line_height 0 0 @line_height;
        }
    }

    .@{name}-shadow {
        height: 4px;
        bottom: @bottom;
        background-color: fade(@line_color, 50%);
    }

    .@{name}-handle {
        top: 26px;
        width: @handle_width;
        height: @handle_height;
        border: 4px solid @handle_color_1;
        background-color: @handle_color_2;
        border-radius: @handle_width;
        box-shadow: 0 1px 3px rgba(0, 0, 255, 0.3);

        &.state_hover,
        &:hover {
            background-color: @handle_color_3;
        }
    }

    .@{name}-min,
    .@{name}-max {
        color: @minmax_text_color;
        font-size: 14px;
        line-height: 1;
        top: 0;
        padding: 3px 5px;
        background-color: @minmax_bg_color;
        border-radius: @line_height;
    }

    .@{name}-from,
    .@{name}-to,
    .@{name}-single {
        font-size: 14px;
        line-height: 1;
        text-shadow: none;
        padding: 3px 5px;
        background-color: @label_color_1;
        color: @label_color_2;
        border-radius: @line_height;

        &:before {
            position: absolute;
            display: block;
            content: "";
            bottom: -6px;
            left: 50%;
            width: 0;
            height: 0;
            margin-left: -3px;
            overflow: hidden;
            border: 3px solid transparent;
            border-top-color: @label_color_1;
        }
    }

    .@{name}-grid {
        height: 25px;

        &-pol {
            background-color: @grid_color_1;
        }

        &-text {
            color: @grid_color_2;
            font-size: 13px;
        }
    }
}
