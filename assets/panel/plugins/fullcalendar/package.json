{"_from": "@fullcalendar/core@4.3.1", "_id": "@fullcalendar/core@4.3.1", "_inBundle": false, "_integrity": "sha512-Eh+p/wpMkWGu26f8NpfQK9ecQMoZxX/aopv+0+4/CH+Ip0paP6iEc40JYgTz7RFl0bFqV1dvwyGyUZ4+9ZeySA==", "_location": "/@fullcalendar/core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@fullcalendar/core@4.3.1", "name": "@fullcalendar/core", "escapedName": "@fullcalendar%2fcore", "scope": "@fullcalendar", "rawSpec": "4.3.1", "saveSpec": null, "fetchSpec": "4.3.1"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/@fullcalendar/core/-/core-4.3.1.tgz", "_shasum": "a061c6d2e998d4155439dbc8aefdfe01cdf648d8", "_spec": "@fullcalendar/core@4.3.1", "_where": "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://arshaw.com/"}, "bugs": {"url": "https://fullcalendar.io/reporting-bugs"}, "bundleDependencies": false, "copyright": "2019 <PERSON>", "deprecated": false, "description": "Provides core functionality, including the Calendar class", "docs": "https://fullcalendar.io/docs/initialize-es6", "homepage": "https://fullcalendar.io/", "keywords": ["calendar", "event", "full-sized"], "license": "MIT", "main": "main.js", "module": "main.esm.js", "name": "@fullcalendar/core", "repository": {"type": "git", "url": "git+https://github.com/fullcalendar/fullcalendar.git", "homepage": "https://github.com/fullcalendar/fullcalendar"}, "title": "FullCalendar Core Package", "types": "main.d.ts", "unpkg": "main.min.js", "version": "4.3.1"}