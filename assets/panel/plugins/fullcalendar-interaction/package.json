{"_from": "@fullcalendar/interaction@4.3.0", "_id": "@fullcalendar/interaction@4.3.0", "_inBundle": false, "_integrity": "sha512-kaKV+tdgH/oIrwTSMGYFec989L5r+KMYJ9ybwLc8G3LbMVcebo8fAlN9VizmGQAuopKfyvOw8yzJdjfmVCCRYQ==", "_location": "/@fullcalendar/interaction", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@fullcalendar/interaction@4.3.0", "name": "@fullcalendar/interaction", "escapedName": "@fullcalendar%2finteraction", "scope": "@fullcalendar", "rawSpec": "4.3.0", "saveSpec": null, "fetchSpec": "4.3.0"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/@fullcalendar/interaction/-/interaction-4.3.0.tgz", "_shasum": "a52d22e4fa2666d5032c77afde75ecfad95c0f3b", "_spec": "@fullcalendar/interaction@4.3.0", "_where": "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://arshaw.com/"}, "bugs": {"url": "https://fullcalendar.io/reporting-bugs"}, "bundleDependencies": false, "copyright": "2019 <PERSON>", "deprecated": false, "description": "Provides functionality for event drag-n-drop, resizing, dateClick, and selectable actions", "docs": "https://fullcalendar.io/docs/editable", "homepage": "https://fullcalendar.io/", "keywords": ["calendar", "event", "full-sized"], "license": "MIT", "main": "main.js", "module": "main.esm.js", "name": "@fullcalendar/interaction", "peerDependencies": {"@fullcalendar/core": "~4.3.0"}, "repository": {"type": "git", "url": "git+https://github.com/fullcalendar/fullcalendar.git", "homepage": "https://github.com/fullcalendar/fullcalendar"}, "title": "FullCalendar Interaction Plugin", "types": "main.d.ts", "unpkg": "main.min.js", "version": "4.3.0"}