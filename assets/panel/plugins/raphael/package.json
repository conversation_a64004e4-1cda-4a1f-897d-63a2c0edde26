{"_from": "<PERSON><PERSON><PERSON>@2.3.0", "_id": "<PERSON><PERSON><PERSON>@2.3.0", "_inBundle": false, "_integrity": "sha512-w2yIenZAQnp257XUWGni4bLMVxpUpcIl7qgxEgDIXtmSypYtlNxfXWpOBxs7LBTps5sDwhRnrToJrMUrivqNTQ==", "_location": "/rap<PERSON>el", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "<PERSON><PERSON><PERSON>@2.3.0", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "2.3.0", "saveSpec": null, "fetchSpec": "2.3.0"}, "_requiredBy": ["#USER", "/", "/jquery-mapael"], "_resolved": "https://registry.npmjs.org/raphael/-/raphael-2.3.0.tgz", "_shasum": "eabeb09dba861a1d4cee077eaafb8c53f3131f89", "_spec": "<PERSON><PERSON><PERSON>@2.3.0", "_where": "/Users/<USER>/Projekte/GitHub/REJack/AdminLTE", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/DmitryBaranovskiy/raphael/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/tomasAlabes"}], "dependencies": {"eve-raphael": "0.5.0"}, "deprecated": false, "description": "JavaScript Vector Library", "devDependencies": {"qunitjs": "^1.0.0", "webpack": "4.39.2", "webpack-cli": "3.3.6"}, "homepage": "http://dmitrybaranovskiy.github.io/raphael/", "keywords": ["svg", "vml", "javascript"], "license": "MIT", "main": "raphael.min.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/DmitryBaranovskiy/raphael.git"}, "scripts": {"build-all": "yarn build-src && yarn build-no-deps && yarn build-no-deps-min && yarn build-prod", "build-no-deps": "webpack --env.noDeps -d --devtool none --output-filename raphael.no-deps.js", "build-no-deps-min": "webpack --env.noDeps -p --output-filename raphael.no-deps.min.js", "build-prod": "webpack -p --output-filename raphael.min.js", "build-src": "webpack -d --devtool none", "dev": "webpack -d", "prepublishOnly": "yarn build-all", "start": "yarn build-all", "test": "echo \"Open dev/test/index.html with your browser\" && exit 1"}, "version": "2.3.0"}