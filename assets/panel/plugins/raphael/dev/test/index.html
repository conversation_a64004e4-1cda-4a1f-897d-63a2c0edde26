<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Raphael Test Suite</title>
    <link rel="stylesheet" href="../../node_modules/qunitjs/qunit/qunit.css" />
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>

    <script type="text/javascript" src="../../node_modules/qunitjs/qunit/qunit.js"></script>
    <script type="text/javascript" src="../../raphael.js"></script>

    <script type="text/javascript">
    (function() {
        var tests = [
            "dom"
        ];
        var typeFolder = Raphael.type.toLowerCase();

        for (var i = 0; i < tests.length; i++) {
            var s = document.createElement('script');
            s.type = "text/javascript";
            s.src = typeFolder + "/" + tests[i] + ".js";
            document.body.appendChild(s);
        }
    })();
    </script>
</body>
</html>