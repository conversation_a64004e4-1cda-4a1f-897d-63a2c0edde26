/*!
 * OverlayScrollbars
 * https://github.com/KingSora/OverlayScrollbars
 *
 * Version: 1.9.1
 *
 * Copyright KingSora | <PERSON>.
 * https://github.com/KingSora
 *
 * Released under the MIT license.
 * Date: 03.08.2019
 */
!function(t,r){"function"==typeof define&&define.amd?define(["jquery"],function(n){return r(t,t.document,undefined,n)}):"object"==typeof module&&"object"==typeof module.exports?module.exports=r(t,t.document,undefined,require("jquery")):r(t,t.document,undefined,t.jQuery)}("undefined"!=typeof window?window:this,function(st,vt,Ti,n){"use strict";var i,o,dt,u,f,L,R,t,r,e,a,c,l,ht,pt="object",bt="function",mt="array",yt="string",gt="boolean",wt="number",s="null",xt="class",Oi="style",_t="id",ki="length",St="prototype",Ci="offsetHeight",Ai="clientHeight",Ni="scrollHeight",Hi="offsetWidth",Li="clientWidth",Ri="scrollWidth",zt="hasOwnProperty",Tt={e:{},o:{},u:["-webkit-","-moz-","-o-","-ms-"],s:["WebKit","Moz","O","MS"],v:function(n){var t=this.o;if(t[n])return t[n];for(var r,e,i=this.u,o=this.d(n),a=vt.createElement("div")[Oi],u=0,f=0;u<i.length;u++)for(e=i[u].replace(/-/g,""),r=[n,i[u]+n,e+o,this.d(e)+o],f=0;f<r[ki];f++)if(a[r[f]]!==Ti)return t[n]=r[f],r[f];return null},p:function(n,t,r){var e=this.s,i=this.e,o=0,a=i[n];if(!a){for(a=st[n];o<e[ki];o++)a=a||st[(t?e[o]:e[o].toLowerCase())+this.d(n)];i[n]=a}return a||r},d:function(n){return n.charAt(0).toUpperCase()+n.slice(1)}},Di={wW:function(){return st.innerWidth||vt.documentElement[Li]||vt.body[Li]},wH:function(){return st.innerHeight||vt.documentElement[Ai]||vt.body[Ai]},mO:function(){return Tt.p("MutationObserver",!0)},rO:function(){return Tt.p("ResizeObserver",!0)},rAF:function(){return Tt.p("requestAnimationFrame",!1,function(n){return st.setTimeout(n,1e3/60)})},cAF:function(){return Tt.p("cancelAnimationFrame",!1,function(n){return st.clearTimeout(n)})},now:function(){return Date.now&&Date.now()||(new Date).getTime()},stpP:function(n){n.stopPropagation?n.stopPropagation():n.cancelBubble=!0},prvD:function(n){n.preventDefault&&n.cancelable?n.preventDefault():n.returnValue=!1},page:function(n){var t="page",r="client",e="X",i=((n=n.originalEvent||n).target||n.srcElement||vt).ownerDocument||vt,o=i.documentElement,a=i.body;if(n.touches===Ti)return!n[t+e]&&n[r+e]&&null!=n[r+e]?{x:n[r+e]+(o&&o.scrollLeft||a&&a.scrollLeft||0)-(o&&o.clientLeft||a&&a.clientLeft||0),y:n[r+"Y"]+(o&&o.scrollTop||a&&a.scrollTop||0)-(o&&o.clientTop||a&&a.clientTop||0)}:{x:n[t+e],y:n.pageY};var u=n.touches[0];return{x:u[t+e],y:u.pageY}},mBtn:function(n){var t=n.button;return n.which||t===Ti?n.which:1&t?1:2&t?3:4&t?2:0},inA:function(n,t){for(var r=0;r<t[ki];r++)try{if(t[r]===n)return r}catch(e){}return-1},isA:function(n){var t=Array.isArray;return t?t(n):this.type(n)==mt},type:function(n){return n===Ti?n+"":null===n?n+"":Object[St].toString.call(n).replace(/^\[object (.+)\]$/,"$1").toLowerCase()},bind:function(n,t){if(typeof n!=bt)throw"Can't bind function!";function r(){}function e(){return n.apply(this instanceof r?this:t,o.concat(Array[i].slice.call(arguments)))}var i=St,o=Array[i].slice.call(arguments,2);return n[i]&&(r[i]=n[i]),e[i]=new r,e}},Ii=Math,Ot=n,Mi=(n.easing,n),kt=(i=[],o="__overlayScrollbars__",function(n,t){var r=arguments[ki];if(r<1)return i;if(t)n[o]=t,i.push(n);else{var e=Di.inA(n,i);if(-1<e){if(!(1<r))return i[e][o];delete n[o],i.splice(e,1)}}}),v=(l=[],L=Di.type,c={className:["os-theme-dark",[s,yt]],resize:["none","n:none b:both h:horizontal v:vertical"],sizeAutoCapable:t=[!0,gt],clipAlways:t,normalizeRTL:t,paddingAbsolute:r=[!(R=[gt,wt,yt,mt,pt,bt,s]),gt],autoUpdate:[null,[s,gt]],autoUpdateInterval:[33,wt],nativeScrollbarsOverlaid:{showNativeScrollbars:r,initialize:t},overflowBehavior:{x:["scroll",a="v-h:visible-hidden v-s:visible-scroll s:scroll h:hidden"],y:["scroll",a]},scrollbars:{visibility:["auto","v:visible h:hidden a:auto"],autoHide:["never","n:never s:scroll l:leave m:move"],autoHideDelay:[800,wt],dragScrolling:t,clickScrolling:r,touchSupport:t,snapHandle:r},textarea:{dynWidth:r,dynHeight:r,inheritedAttrs:[["style","class"],[yt,mt,s]]},callbacks:{onInitialized:e=[null,[s,bt]],onInitializationWithdrawn:e,onDestroyed:e,onScrollStart:e,onScroll:e,onScrollStop:e,onOverflowChanged:e,onOverflowAmountChanged:e,onDirectionChanged:e,onContentSizeChanged:e,onHostSizeChanged:e,onUpdated:e}},ht={m:d(),g:d(!0),_:function(n,t,C,r){var e={},i={},o=Mi.extend(!0,{},n),A=Mi.inArray,N=Mi.isEmptyObject,H=function(n,t,r,e,i,o){for(var a in t)if(t[zt](a)&&n[zt](a)){var u,f,c,l,s,v,d,h,p=!1,b=!1,m=t[a],y=L(m),g=y==pt,w=L(m)!=mt?[m]:m,x=r[a],_=n[a],S=L(_),z=o?o+".":"",T='The option "'+z+a+"\" wasn't set, because",O=[],k=[];if(x=x===Ti?{}:x,g&&S==pt)e[a]={},i[a]={},H(_,m,x,e[a],i[a],z+a),Mi.each([n,e,i],function(n,t){N(t[a])&&delete t[a]});else if(!g){for(v=0;v<w[ki];v++)if(s=w[v],c=(y=L(s))==yt&&-1===A(s,R))for(O.push(yt),u=s.split(" "),k=k.concat(u),d=0;d<u[ki];d++){for(l=(f=u[d].split(":"))[0],h=0;h<f[ki];h++)if(_===f[h]){p=!0;break}if(p)break}else if(O.push(s),S===s){p=!0;break}p?((b=_!==x)&&(e[a]=_),(c?A(x,f)<0:b)&&(i[a]=c?l:_)):C&&console.warn(T+" it doesn't accept the type [ "+S.toUpperCase()+' ] with the value of "'+_+'".\r\nAccepted types are: [ '+O.join(", ").toUpperCase()+" ]."+(0<k[length]?"\r\nValid strings are: [ "+k.join(", ").split(":").join(", ")+" ].":"")),delete n[a]}}};return H(o,t,r||{},e,i),!N(o)&&C&&console.warn("The following options are discarded due to invalidity:\r\n"+st.JSON.stringify(o,null,2)),{S:e,z:i}}},(dt=st.OverlayScrollbars=function(n,r,e){if(0===arguments[ki])return this;var i,t,o=[],a=Mi.isPlainObject(r);return n?(n=n[ki]!=Ti?n:[n[0]||n],h(),0<n[ki]&&(a?Mi.each(n,function(n,t){(i=t)!==Ti&&o.push(m(i,r,e,u,f))}):Mi.each(n,function(n,t){i=kt(t),"!"===r&&dt.valid(i)||Di.type(r)==bt&&r(t,i)?o.push(i):r===Ti&&o.push(i)}),t=1===o[ki]?o[0]:o),t):a||!r?t:o}).globals=function(){h();var n=Mi.extend(!0,{},u);return delete n.msie,n},dt.defaultOptions=function(n){h();var t=u.defaultOptions;if(n===Ti)return Mi.extend(!0,{},t);u.defaultOptions=Mi.extend(!0,{},t,ht._(n,ht.g,!0,t).S)},dt.valid=function(n){return n instanceof dt&&!n.getState().destroyed},dt.extension=function(n,t,r){var e=Di.type(n)==yt,i=arguments[ki],o=0;if(i<1||!e)return Mi.extend(!0,{length:l[ki]},l);if(e)if(Di.type(t)==bt)l.push({name:n,extensionFactory:t,defaultOptions:r});else for(;o<l[ki];o++)if(l[o].name===n){if(!(1<i))return Mi.extend(!0,{},l[o]);l.splice(o,1)}},dt);function d(i){var o=function(n){var t,r,e;for(t in n)n[zt](t)&&(r=n[t],(e=L(r))==mt?n[t]=r[i?1:0]:e==pt&&(n[t]=o(r)));return n};return o(Mi.extend(!0,{},c))}function h(){u=u||new p(ht.m),f=f||new b(u)}function p(n){var _=this,i="overflow",S=Mi("body"),z=Mi('<div id="os-dummy-scrollbar-size"><div></div></div>'),o=z[0],e=Mi(z.children("div").eq(0));S.append(z),z.hide().show();var t,r,a,u,f,c,l,s,v,d=T(o),h={x:0===d.x,y:0===d.y};function T(n){return{x:n[Ci]-n[Ai],y:n[Hi]-n[Li]}}Mi.extend(_,{defaultOptions:n,autoUpdateLoop:!1,autoUpdateRecommended:!Di.mO(),nativeScrollbarSize:d,nativeScrollbarIsOverlaid:h,nativeScrollbarStyling:function(){var n=!1;z.addClass("os-viewport-native-scrollbars-invisible");try{n="none"===z.css("scrollbar-width")||"none"===st.getComputedStyle(o,"::-webkit-scrollbar").getPropertyValue("display")}catch(t){}return n}(),overlayScrollbarDummySize:{x:30,y:30},msie:(r=st.navigator.userAgent,a="indexOf",u="substring",f=r[a]("MSIE "),c=r[a]("Trident/"),l=r[a]("Edge/"),s=r[a]("rv:"),v=parseInt,0<f?t=v(r[u](f+5,r[a](".",f)),10):0<c?t=v(r[u](s+3,r[a](".",s)),10):0<l&&(t=v(r[u](l+5,r[a](".",l)),10)),t),cssCalc:function(){for(var n,t=vt.createElement("div")[Oi],r=-1;r<Tt.u[ki];r++)if(n=r<0?"calc":Tt.u[r]+"calc",t.cssText="width:"+n+"(1px);",t[ki])return n;return null}(),restrictedMeasuring:function(){z.css(i,"hidden");var n=o[Ri],t=o[Ni];z.css(i,"visible");var r=o[Ri],e=o[Ni];return n-r!=0||t-e!=0}(),rtlScrollBehavior:function(){z.css({"overflow-y":"hidden","overflow-x":"scroll",direction:"rtl"}).scrollLeft(0);var n=z.offset(),t=e.offset();z.scrollLeft(999);var r=e.offset();return{i:n.left===t.left,n:t.left-r.left==0}}(),supportTransform:null!==Tt.v("transform"),supportTransition:null!==Tt.v("transition"),supportPassiveEvents:function(){var n=!1;try{st.addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){n=!0}}))}catch(t){}return n}(),supportResizeObserver:!!Di.rO(),supportMutationObserver:!!Di.mO()}),z.removeAttr(Oi).remove(),function(){if(!h.x||!h.y){var m=Ii.abs,y=Di.wW(),g=Di.wH(),w=x();Mi(st).on("resize",function(){if(0<kt().length){var n=Di.wW(),t=Di.wH(),r=n-y,e=t-g;if(0==r&&0==e)return;var i,o=Ii.round(n/(y/100)),a=Ii.round(t/(g/100)),u=m(r),f=m(e),c=m(o),l=m(a),s=x(),v=2<u&&2<f,d=!function b(n,t){var r=m(n),e=m(t);return!(r===e||r+1===e||r-1===e)}(c,l),h=v&&d&&(s!==w&&0<w),p=_.nativeScrollbarSize;h&&(S.append(z),i=_.nativeScrollbarSize=T(z[0]),z.remove(),p.x===i.x&&p.y===i.y||Mi.each(kt(),function(){kt(this)&&kt(this).update("zoom")})),y=n,g=t,w=s}})}function x(){var n=st.screen.deviceXDPI||0,t=st.screen.logicalXDPI||1;return st.devicePixelRatio||n/t}}()}function b(r){var c,e=Mi.inArray,l=Di.now,s="autoUpdate",v=ki,d=[],h=[],p=!1,b=33,m=l(),y=function(){if(0<d[v]&&p){c=Di.rAF()(function(){y()});var n,t,r,e,i,o,a=l(),u=a-m;if(b<u){m=a-u%b,n=33;for(var f=0;f<d[v];f++)(t=d[f])!==Ti&&(e=(r=t.options())[s],i=Ii.max(1,r.autoUpdateInterval),o=l(),(!0===e||null===e)&&o-h[f]>i&&(t.update("auto"),h[f]=new Date(o+=i)),n=Ii.max(1,Ii.min(n,i)));b=n}}else b=33};this.add=function(n){-1===e(n,d)&&(d.push(n),h.push(l()),0<d[v]&&!p&&(p=!0,r.autoUpdateLoop=p,y()))},this.remove=function(n){var t=e(n,d);-1<t&&(h.splice(t,1),d.splice(t,1),0===d[v]&&p&&(p=!1,r.autoUpdateLoop=p,c!==Ti&&(Di.cAF()(c),c=-1)))}}function m(r,n,t,Nt,Ht){var ln=Di.type,sn=Mi.inArray,s=Mi.each,Lt=new dt,e=Mi[St];if(ot(r)){if(kt(r)){var i=kt(r);return i.options(n),i}var Rt,Dt,It,Mt,M,Wt,jt,Et,W,vn,Ft,A,l,Ut,Pt,qt,Vt,Bt,y,u,Xt,Yt,$t,Kt,Gt,Jt,Qt,Zt,nr,tr,o,a,rr,er,ir,f,j,v,E,or,ar,ur,fr,cr,lr,sr,vr,dr,hr,pr,c,d,h,p,b,m,g,N,br,mr,yr,H,gr,wr,xr,_r,Sr,zr,Tr,Or,kr,Cr,Ar,Nr,Hr,Lr,Rr,Dr,L,Ir,Mr,Wr,jr,Er,Fr,Ur,Pr,w,x,qr,Vr,Br,Xr,Yr,$r,Kr,Gr,Jr,Qr,Zr,ne,te,re,_,ee,S,z,T,O,ie,k,oe,C,R,ae,ue,fe,ce,le,F,U,D,se,ve,de,he,pe,be={},dn={},hn={},me={},ye={},I="-hidden",ge="margin-",we="padding-",xe="border-",_e="top",Se="right",ze="bottom",Te="left",Oe="min-",ke="max-",Ce="width",Ae="height",Ne="float",He="",Le="auto",pn="sync",Re="scroll",De="100%",bn="x",mn="y",P=".",Ie=" ",q="scrollbar",V="-horizontal",B="-vertical",Me=Re+"Left",We=Re+"Top",je="mousedown touchstart",X="mouseup touchend touchcancel",Y="mousemove touchmove",$="mouseenter",K="mouseleave",G="keydown",J="keyup",Q="selectstart",Z="transitionend webkitTransitionEnd oTransitionEnd",nn="__overlayScrollbarsRO__",tn="os-",rn="os-html",en="os-host",on=en+"-textarea",an=en+"-"+q+V+I,un=en+"-"+q+B+I,fn=en+"-transition",Ee=en+"-rtl",Fe=en+"-resize-disabled",Ue=en+"-scrolling",Pe=en+"-overflow",qe=Pe+"-x",Ve=Pe+"-y",cn="os-textarea",yn=cn+"-cover",gn="os-padding",wn="os-viewport",Be=wn+"-native-scrollbars-invisible",xn=wn+"-native-scrollbars-overlaid",_n="os-content",Xe="os-content-arrange",Ye="os-content-glue",$e="os-size-auto-observer",Sn="os-resize-observer",zn="os-resize-observer-item",Tn=zn+"-final",On="os-text-inherit",kn=tn+q,Cn=kn+"-track",An=Cn+"-off",Nn=kn+"-handle",Hn=Nn+"-off",Ln=kn+"-unusable",Rn=kn+"-"+Le+I,Dn=kn+"-corner",Ke=Dn+"-resize",Ge=Ke+"-both",Je=Ke+V,Qe=Ke+B,In=kn+V,Mn=kn+B,Wn="os-dragging",Ze="os-theme-none",jn=[],En={},ni={},ti=42,Fn=[],Un={},Pn=["wrap","cols","rows"],qn=[_t,xt,Oi,"open"];if(Lt.sleep=function(){tr=!0},Lt.update=function(n){var t,r,e,i,o,a=ln(n)==yt;return a?n===Le?(t=function u(){if(!tr&&!ie){var r,e,i,o,n=[{T:ar,O:qn.concat(":visible")},{T:Vt?or:Ti,O:Pn}];return s(n,function(n,t){(e=t.T)&&s(t.O,function(n,t){i=":"===t.charAt(0)?e.is(t):e.attr(t),o=Un[t],r=r||xi(i,o),Un[t]=i})}),r}}(),r=function l(){if(tr)return!1;var n,t,r,e,i,o=gi(),a=Vt&&kr&&!Jr?or.val().length:0,u=!ie&&kr&&!Vt,f={},c={};return Ut&&(f={x:wr[Ri],y:wr[Ni]}),u&&(n=sr.css(Ne),c[Ne]=nr?Se:Te,c[Ce]=Le,sr.css(c)),i={w:o[Ri]+a,h:o[Ni]+a},u&&(c[Ne]=n,c[Ce]=De,sr.css(c)),t=ui(),r=xi(i,w),e=xi(f,_),w=i,_=f,r||t||e}(),(e=t||r)&&ci({k:r,C:Pt?Ti:rr})):n===pn?ie?(i=T(S.takeRecords()),o=O(z.takeRecords())):i=Lt.update(Le):"zoom"===n&&ci({A:!0,k:!0}):(n=tr||n,tr=!1,Lt.update(pn)&&!n||ci({N:n})),Vt||sr.find("img").each(function(n,t){-1===Di.inA(t,Fn)&&Mi(t).off("load",Gn).on("load",Gn)}),e||i||o},Lt.options=function(n,t){var r,e={};if(Mi.isEmptyObject(n)||!Mi.isPlainObject(n)){if(ln(n)!=yt)return a;if(!(1<arguments.length))return ut(a,n);!function f(n,t,r){for(var e=t.split(P),i=e.length,o=0,a={},u=a;o<i;o++)a=a[e[o]]=o+1<i?{}:r;Mi.extend(n,u,!0)}(e,n,t),r=Qn(e)}else r=Qn(n);Mi.isEmptyObject(r)||ci({C:r})},Lt.destroy=function(){for(var n in Ht.remove(Lt),oi(),ei(fr),ei(ur),En)Lt.removeExt(n);ai(!0),nt(!0),dr&&ft(dr),vr&&ft(vr),Yt&&ft(ur),tt(!0),et(!0),Zn(!0);for(var t=0;t<Fn[ki];t++)Mi(Fn[t]).off("load",Gn);Fn=Ti,tr=qt=!0,kt(r,0),bi("onDestroyed")},Lt.scroll=function(n,t,r,e){if(0===arguments.length||n===Ti){var i=dn,o=hn,a=$r&&nr&&It.i,u=$r&&nr&&It.n,f=i.H,c=i.L,l=i.R;return c=a?1-c:c,f=a?l-f:f,l*=u?-1:1,{position:{x:f*=u?-1:1,y:o.H},ratio:{x:c,y:o.L},max:{x:l,y:o.R},handleOffset:{x:i.D,y:o.D},handleLength:{x:i.I,y:o.I},handleLengthRatio:{x:i.M,y:o.M},trackLength:{x:i.W,y:o.W},snappedHandleOffset:{x:i.j,y:o.j},isRTL:nr,isRTLNormalized:$r}}Lt.update(pn);function s(n,t){for(b=0;b<t[W];b++)if(n===t[b])return!0;return!1}function v(n,t){var r=n?O:k;if(t=ln(t)==yt||ln(t)==wt?[t,t]:t,ln(t)==mt)return n?t[0]:t[1];if(ln(t)==pt)for(b=0;b<r[W];b++)if(r[b]in t)return t[r[b]]}function d(n,t){var r,e,i,o,a=ln(t)==yt,u=n?dn:hn,f=u.H,c=u.R,l=nr&&n,s=l&&It.n&&!T,v="replace",d=eval;if((e=a?(2<t[W]&&(o=t.substr(0,2),-1<sn(o,C)&&(r=o)),t=(t=r?t.substr(2):t)[v](/min/g,0)[v](/</g,0)[v](/max/g,(s?"-":He)+De)[v](/>/g,(s?"-":He)+De)[v](/px/g,He)[v](/%/g," * "+c*(l&&It.n?-1:1)/100)[v](/vw/g," * "+me.w)[v](/vh/g," * "+me.h),yi(isNaN(t)?yi(d(t),!0).toFixed():t)):t)!==Ti&&!isNaN(e)&&ln(e)==wt){var h=T&&l,p=f*(h&&It.n?-1:1),b=h&&It.i,m=h&&It.n;switch(p=b?c-p:p,r){case"+=":i=p+e;break;case"-=":i=p-e;break;case"*=":i=p*e;break;case"/=":i=p/e;break;default:i=e}i=b?c-i:i,i*=m?-1:1,i=l&&It.n?Ii.min(0,Ii.max(c,i)):Ii.max(0,Ii.min(c,i))}return i===f?Ti:i}function h(n,t,r,e){var i,o,a=[r,r],u=ln(n);if(u==t)n=[n,n];else if(u==mt){if(2<(i=n[W])||i<1)n=a;else for(1===i&&(n[1]=r),b=0;b<i;b++)if(o=n[b],ln(o)!=t||!s(o,e)){n=a;break}}else n=u==pt?[n[bn]||r,n[mn]||r]:a;return{x:n[0],y:n[1]}}function p(n){var t,r,e=[],i=[_e,Se,ze,Te];for(b=0;b<n[W]&&b!==i[W];b++)t=n[b],(r=ln(t))==gt?e.push(t?yi(z.css(ge+i[b])):0):e.push(r==wt?t:0);return e}var b,m,y,g,w,x,_,S,z,T=$r,O=[bn,Te,"l"],k=[mn,_e,"t"],C=["+=","-=","*=","/="],A=ln(t)==pt,N=A?t.complete:e,H={},L={},R="begin",D="nearest",I="never",M="ifneeded",W=ki,j=[bn,mn,"xy","yx"],E=[R,"end","center",D],F=["always",I,M],U=n[zt]("el"),P=U?n.el:n,q=!!(P instanceof Mi||Ot)&&P instanceof Ot,V=!q&&ot(P),B=ln(N)!=bt?Ti:function(){m&&di(!0),y&&di(!1),N()};if(q||V){var X,Y=U?n.margin:0,$=U?n.axis:0,K=U?n.scroll:0,G=U?n.block:0,J=[0,0,0,0],Q=ln(Y);if(0===(z=q?P:Mi(P))[W])return;Y=Q==wt||Q==gt?p([Y,Y,Y,Y]):Q==mt?2===(X=Y[W])?p([Y[0],Y[1],Y[0],Y[1]]):4<=X?p(Y):J:Q==pt?p([Y[_e],Y[Se],Y[ze],Y[Te]]):J,w=s($,j)?$:"xy",x=h(K,yt,"always",F),_=h(G,yt,R,E),S=Y;var Z=dn.H,nn=hn.H,tn=cr.offset(),rn=z.offset(),en={x:x.x==I||w==mn,y:x.y==I||w==bn};rn[_e]-=S[0],rn[Te]-=S[3];var on={x:Ii.round(rn[Te]-tn[Te]+Z),y:Ii.round(rn[_e]-tn[_e]+nn)};if(nr&&(It.n||It.i||(on.x=Ii.round(tn[Te]-rn[Te]+Z)),It.n&&T&&(on.x*=-1),It.i&&T&&(on.x=Ii.round(tn[Te]-rn[Te]+(dn.R-Z)))),_.x!=R||_.y!=R||x.x==M||x.y==M||nr){var an=z[0],un=vn?an.getBoundingClientRect():{width:an[Hi],height:an[Ci]},fn={w:un[Ce]+S[3]+S[1],h:un[Ae]+S[0]+S[2]},cn=function(n){var t=pi(n),r=t.F,e=t.U,i=t.P,o=_[i]==(n&&nr?R:"end"),a="center"==_[i],u=_[i]==D,f=x[i]==I,c=x[i]==M,l=me[r],s=tn[e],v=fn[r],d=rn[e],h=a?2:1,p=d+v/2,b=s+l/2,m=v<=l&&s<=d&&d+v<=s+l;f?en[i]=!0:en[i]||((u||c)&&(en[i]=c&&m,o=v<l?b<p:p<b),on[i]-=o||a?(l/h-v/h)*(n&&nr&&T?-1:1):0)};cn(!0),cn(!1)}en.y&&delete on.y,en.x&&delete on.x,n=on}H[Me]=d(!0,v(!0,n)),H[We]=d(!1,v(!1,n)),m=H[Me]!==Ti,y=H[We]!==Ti,(m||y)&&(0<t||A)?A?(t.complete=B,lr.animate(H,t)):(g={duration:t,complete:B},ln(r)==mt||Mi.isPlainObject(r)?(L[Me]=r[0]||r.x,L[We]=r[1]||r.y,g.specialEasing=L):g.easing=r,lr.animate(H,g)):(m&&lr[Me](H[Me]),y&&lr[We](H[We]))},Lt.scrollStop=function(n,t,r){return lr.stop(n,t,r),Lt},Lt.getElements=function(n){var t={target:br,host:mr,padding:gr,viewport:wr,content:xr,scrollbarHorizontal:{scrollbar:c[0],track:d[0],handle:h[0]},scrollbarVertical:{scrollbar:p[0],track:b[0],handle:m[0]},scrollbarCorner:pr[0]};return ln(n)==yt?ut(t,n):t},Lt.getState=function(n){function t(n){if(!Mi.isPlainObject(n))return n;function t(n,t){r[zt](n)&&(r[t]=r[n],delete r[n])}var r=_i({},n);return t("w",Ce),t("h",Ae),delete r.c,r}var r={destroyed:!!t(qt),sleeping:!!t(tr),autoUpdate:t(!ie),widthAuto:t(kr),heightAuto:t(Cr),padding:t(Hr),overflowAmount:t(Er),hideOverflow:t(Or),hasOverflow:t(Tr),contentScrollSize:t(Sr),viewportSize:t(me),hostSize:t(_r),documentMixed:t(y)};return ln(n)==yt?ut(r,n):r},Lt.ext=function(n){var t,r="added removed on contract".split(" "),e=0;if(ln(n)==yt){if(En[zt](n))for(t=_i({},En[n]);e<r.length;e++)delete t[r[e]]}else for(e in t={},En)t[e]=_i({},Lt.ext(e));return t},Lt.addExt=function(n,t){var r,e,i,o,a=dt.extension(n),u=!0;if(a){if(En[zt](n))return Lt.ext(n);if((r=a.extensionFactory.call(Lt,_i({},a.defaultOptions),Mi,Di))&&(i=r.contract,ln(i)==bt&&(o=i(st),u=ln(o)==gt?o:u),u))return e=(En[n]=r).added,ln(e)==bt&&e(t),Lt.ext(n)}else console.warn('A extension with the name "'+n+"\" isn't registered.")},Lt.removeExt=function(n){var t,r=En[n];return!!r&&(delete En[n],t=r.removed,ln(t)==bt&&t(),!0)},function lt(n,t,r){return o=Nt.defaultOptions,Wt=Nt.nativeScrollbarStyling,Et=_i({},Nt.nativeScrollbarSize),Rt=_i({},Nt.nativeScrollbarIsOverlaid),Dt=_i({},Nt.overlayScrollbarDummySize),It=_i({},Nt.rtlScrollBehavior),Qn(_i({},o,t)),Rt.x&&Rt.x&&!rr.nativeScrollbarsOverlaid.initialize?(bi("onInitializationWithdrawn"),!1):(jt=Nt.cssCalc,M=Nt.msie,Mt=Nt.autoUpdateRecommended,W=Nt.supportTransition,vn=Nt.supportTransform,Ft=Nt.supportPassiveEvents,A=Nt.supportResizeObserver,l=Nt.supportMutationObserver,Ut=Nt.restrictedMeasuring,j=Mi(n.ownerDocument),N=j[0],f=Mi(N.defaultView||N.parentWindow),g=f[0],v=ct(j,"html"),E=ct(v,"body"),or=Mi(n),br=or[0],Vt=or.is("textarea"),Bt=or.is("body"),y=N!==vt,Bt&&((e={}).l=Ii.max(or[Me](),v[Me](),f[Me]()),e.t=Ii.max(or[We](),v[We](),f[We]())),Zn(),nt(),tt(),rt(!0),rt(!1),et(),function c(){var t=g.top!==g,e={},i={},o={};function r(n){if(u(n)){var t=f(n),r={};(de||ve)&&(r[Ce]=i.w+(t.x-e.x)*o.x),(he||ve)&&(r[Ae]=i.h+(t.y-e.y)*o.y),ar.css(r),Di.stpP(n)}else a(n)}function a(n){var t=n!==Ti;j.off(Q,Kn).off(Y,r).off(X,a),zi(E,Wn),pr.releaseCapture&&pr.releaseCapture(),t&&(D&&ii(),Lt.update(Le)),D=!1}function u(n){var t=(n.originalEvent||n).touches!==Ti;return!tr&&!qt&&(1===Di.mBtn(n)||t)}function f(n){return M&&t?{x:n.screenX,y:n.screenY}:Di.page(n)}pe=function(n){u(n)&&(ie&&(D=!0,oi()),e=f(n),i.w=mr[Hi]-(Xt?0:$t),i.h=mr[Ci]-(Xt?0:Kt),o=it(),j.on(Q,Kn).on(Y,r).on(X,a),Si(E,Wn),pr.setCapture&&pr.setCapture(),Di.prvD(n),Di.stpP(n))}}(),Vn(),Bt&&(lr[Me](e.l)[We](e.t),vt.activeElement==n&&wr.focus&&(lr.attr("tabindex","-1"),wr.focus(),lr.one(je,function(){lr.removeAttr("tabindex")}))),ei(fr,Bn),Lt.update(Le),Pt=!0,bi("onInitialized"),s(jn,function(n,t){bi(t.n,t.a)}),jn=[],ln(r)==yt&&(r=[r]),Di.isA(r)?s(r,function(n,t){Lt.addExt(t)}):Mi.isPlainObject(r)&&s(r,function(n,t){Lt.addExt(n,t)}),setTimeout(function(){W&&!qt&&Si(ar,fn)},333),Pt);var e}(r,n,t))return kt(r,Lt),Lt;Lt=Ti}function ri(n,t,r,e,i){for(var o=e?"removeEventListener":"addEventListener",a=t.split(Ie),u=0;u<a[ki];u++)n[0][o](a[u],r,{passive:!i})}function ei(i,n){if(i)if(n){var o=3333333,t=Di.rO(),r="animationstart mozAnimationStart webkitAnimationStart MSAnimationStart",e="childNodes",a=function(){i[We](o)[Me](nr?It.n?-o:It.i?0:o:o),n()};if(A)((k=i.append(wi(Sn+" observed")).contents()[0])[nn]=new t(a)).observe(k);else if(9<M||!Mt){i.prepend(wi(Sn,wi({className:zn,dir:"ltr"},wi(zn,wi(Tn))+wi(zn,wi({className:Tn,style:"width: 200%; height: 200%"})))));var u,f,c,l,s=i[0][e][0][e][0],v=Mi(s[e][1]),d=Mi(s[e][0]),h=Mi(d[0][e][0]),p=s[Hi],b=s[Ci],m=Nt.nativeScrollbarSize,y=function(){d[Me](o)[We](o),v[Me](o)[We](o)},g=function(){f=0,u&&(p=c,b=l,a())},w=function(n){return c=s[Hi],l=s[Ci],u=c!=p||l!=b,n&&u&&!f?(Di.cAF()(f),f=Di.rAF()(g)):n||g(),y(),n&&(Di.prvD(n),Di.stpP(n)),!1},x={},_={};mi(_,He,[-2*(m.y+1),-2*m.x,-2*m.y,-2*(m.x+1)]),Mi(s).css(_),d.on(Re,w),v.on(Re,w),i.on(r,function(){w(!1)}),x[Ce]=o,x[Ae]=o,h.css(x),y()}else{var S=N.attachEvent,z=M!==Ti;if(S)i.prepend(wi(Sn)),ct(i,P+Sn)[0].attachEvent("onresize",a);else{var T=N.createElement(pt);T.setAttribute("tabindex","-1"),T.setAttribute(xt,Sn),T.onload=function(){var n=this.contentDocument.defaultView;n.addEventListener("resize",a),n.document.documentElement.style.display="none"},T.type="text/html",z&&i.prepend(T),T.data="about:blank",z||i.prepend(T),i.on(r,a)}}if(i[0]===H){var O=function(){var n=ar.css("direction"),t={},r=0,e=!1;return n!==L&&(r="ltr"===n?(t[Te]=0,t[Se]=Le,o):(t[Te]=Le,t[Se]=0,It.n?-o:It.i?0:o),fr.children().eq(0).css(t),i[Me](r)[We](o),L=n,e=!0),e};O(),i.on(Re,function(n){return O()&&ci(),Di.prvD(n),Di.stpP(n),!1})}}else if(A){var k,C=(k=i.contents()[0])[nn];C&&(C.disconnect(),delete k[nn])}else ft(i.children(P+Sn).eq(0))}function Vn(){if(l){var e,i,r,o,a,u,n=Di.mO(),f=Di.now();O=function(n){var t=!1;return Pt&&!tr&&(s(n,function(){return!(t=function o(n){var t=n.attributeName,r=n.target,e=n.type,i="closest";if(r===xr)return null===t;if("attributes"===e&&(t===xt||t===Oi)&&!Vt){if(t===xt&&Mi(r).hasClass(en))return Jn(n.oldValue,r.getAttribute(xt));if(typeof r[i]!=bt)return!0;if(null!==r[i](P+Sn)||null!==r[i](P+kn)||null!==r[i](P+Dn))return!1}return!0}(this))}),t&&(o=Di.now(),a=Cr||kr,u=function(){qt||(f=o,Vt&&fi(),a?ci():Lt.update(Le))},clearTimeout(r),11<o-f||!a?u():r=setTimeout(u,11))),t},S=new n(T=function(n){var t,r=!1;return Pt&&!tr&&(s(n,function(){if(e=(t=this).target,i=t.attributeName,r=i===xt?Jn(t.oldValue,e.className):i!==Oi||t.oldValue!==e[Oi].cssText)return!1}),r&&Lt.update(Le)),r}),z=new n(O)}}function ii(){l&&!ie&&(S.observe(mr,{attributes:!0,attributeOldValue:!0,attributeFilter:qn}),z.observe(Vt?br:xr,{attributes:!0,attributeOldValue:!0,subtree:!Vt,childList:!Vt,characterData:!Vt,attributeFilter:Vt?Pn:qn}),ie=!0)}function oi(){l&&ie&&(S.disconnect(),z.disconnect(),ie=!1)}function Bn(){if(!tr){var n,t={w:H[Ri],h:H[Ni]};n=xi(t,x),x=t,n&&ci({A:!0})}}function Xn(){le&&si(!0)}function Yn(){le&&!E.hasClass(Wn)&&si(!1)}function $n(){ce&&(si(!0),clearTimeout(R),R=setTimeout(function(){ce&&!qt&&si(!1)},100))}function ai(e){function n(n,t,r){Ft?ri(n,t,r,e):n[i](t,r)}var i=e?"off":"on";ce&&!e?n(ar,Y,$n):(e&&n(ar,Y,$n),n(ar,$,Xn),n(ar,K,Yn)),Pt||e||ar.one("mouseover",Xn)}function Kn(n){return Di.prvD(n),!1}function Gn(){ci({k:!0})}function ui(){var n={};return Bt&&vr&&(n.w=yi(vr.css(Oe+Ce)),n.h=yi(vr.css(Oe+Ae)),n.c=xi(n,re),n.f=!0),!!(re=n).c}function Jn(n,t){var r=t!==Ti&&null!==t?t.split(Ie):He,e=n!==Ti&&null!==n?n.split(Ie):He;if(r===He&&e===He)return!1;var i,o,a,u,f,c=function h(n,t){var r,e,i=[],o=[];for(r=0;r<n.length;r++)i[n[r]]=!0;for(r=0;r<t.length;r++)i[t[r]]?delete i[t[r]]:i[t[r]]=!0;for(e in i)o.push(e);return o}(e,r),l=!1,s=Gr!==Ti&&null!==Gr?Gr.split(Ie):[He],v=Kr!==Ti&&null!==Kr?Kr.split(Ie):[He],d=sn(Ze,c);for(-1<d&&c.splice(d,1),o=0;o<c.length;o++)if(0!==(i=c[o]).indexOf(en)){for(f=u=!0,a=0;a<s.length;a++)if(i===s[a]){u=!1;break}for(a=0;a<v.length;a++)if(i===v[a]){f=!1;break}if(u&&f){l=!0;break}}return l}function fi(){if(!tr){var n,t,r,e,i=!Jr,o=me.w,a=me.h,u={},f=kr||i;return u[Oe+Ce]=He,u[Oe+Ae]=He,u[Ce]=Le,or.css(u),n=br[Hi],t=f?Ii.max(n,br[Ri]-1):1,u[Ce]=kr?Le:De,u[Oe+Ce]=De,u[Ae]=Le,or.css(u),r=br[Ci],e=Ii.max(r,br[Ni]-1),u[Ce]=t,u[Ae]=e,hr.css(u),u[Oe+Ce]=o,u[Oe+Ae]=a,or.css(u),{q:n,V:r,B:t,X:e}}}function ci(n){clearTimeout(ir),n=n||{},ni.A|=n.A,ni.k|=n.k,ni.N|=n.N;var t,r=Di.now(),e=!!ni.A,i=!!ni.k,o=!!ni.N,a=n.C,u=0<ti&&Pt&&!qt&&!o&&!a&&r-er<ti&&!Cr&&!kr;if(u&&(ir=setTimeout(ci,ti)),!(qt||u||tr&&!a||Pt&&!o&&(t=ar.is(":hidden"))||"inline"===ar.css("display"))){er=r,ni={},!Wt||Rt.x&&Rt.y?Et=_i({},Nt.nativeScrollbarSize):(Et.x=0,Et.y=0),ye={x:3*(Et.x+(Rt.x?0:3)),y:3*(Et.y+(Rt.y?0:3))};var f=function(){return xi.apply(this,[].slice.call(arguments).concat([o]))},c={x:lr[Me](),y:lr[We]()},l=rr.scrollbars,s=rr.textarea,v=l.visibility,d=f(v,qr),h=l.autoHide,p=f(h,Vr),b=l.clickScrolling,m=f(b,Br),y=l.dragScrolling,g=f(y,Xr),w=rr.className,x=f(w,Kr),_=rr.resize,S=f(_,Yr)&&!Bt,z=rr.paddingAbsolute,T=f(z,Ir),O=rr.clipAlways,k=f(O,Mr),C=rr.sizeAutoCapable&&!Bt,A=f(C,Pr),N=rr.nativeScrollbarsOverlaid.showNativeScrollbars,H=f(N,Fr),L=rr.autoUpdate,R=f(L,Ur),D=rr.overflowBehavior,I=f(D,jr,o),M=s.dynWidth,W=f(te,M),j=s.dynHeight,E=f(ne,j);if(ue="n"===h,fe="s"===h,ce="m"===h,le="l"===h,ae=l.autoHideDelay,Gr=Kr,se="n"===_,ve="b"===_,de="h"===_,he="v"===_,$r=rr.normalizeRTL,N=N&&Rt.x&&Rt.y,qr=v,Vr=h,Br=b,Xr=y,Kr=w,Yr=_,Ir=z,Mr=O,Pr=C,Fr=N,Ur=L,jr=_i({},D),te=M,ne=j,Tr=Tr||{x:!1,y:!1},x&&(zi(ar,Gr+Ie+Ze),Si(ar,w!==Ti&&null!==w&&0<w.length?w:Ze)),R&&(!0===L?(oi(),Ht.add(Lt)):null===L&&Mt?(oi(),Ht.add(Lt)):(Ht.remove(Lt),ii())),A)if(C)if(dr?dr.show():(dr=Mi(wi(Ye)),cr.before(dr)),Yt)ur.show();else{ur=Mi(wi($e)),yr=ur[0],dr.before(ur);var F={w:-1,h:-1};ei(ur,function(){var n={w:yr[Hi],h:yr[Ci]};xi(n,F)&&(Pt&&Cr&&0<n.h||kr&&0<n.w?ci():(Pt&&!Cr&&0===n.h||!kr&&0===n.w)&&ci()),F=n}),Yt=!0,null!==jt&&ur.css(Ae,jt+"(100% + 1px)")}else Yt&&ur.hide(),dr&&dr.hide();o&&(fr.find("*").trigger(Re),Yt&&ur.find("*").trigger(Re));var U,P=f(t=t===Ti?ar.is(":hidden"):t,ee),q=!!Vt&&"off"!==or.attr("wrap"),V=f(q,Jr),B=ar.css("direction"),X=f(B,Dr),Y=ar.css("box-sizing"),$=f(Y,Nr),K={c:o,t:yi(ar.css(we+_e)),r:yi(ar.css(we+Se)),b:yi(ar.css(we+ze)),l:yi(ar.css(we+Te))};try{U=Yt?yr.getBoundingClientRect():null}catch(Ct){return}Xt="border-box"===Y;var G=(nr="rtl"===B)?Te:Se,J=nr?Se:Te,Q=!1,Z=!(!Yt||"none"===ar.css(Ne))&&(0===Ii.round(U.right-U.left)&&(!!z||0<mr[Li]-$t));if(C&&!Z){var nn=mr[Hi],tn=dr.css(Ce);dr.css(Ce,Le);var rn=mr[Hi];dr.css(Ce,tn),(Q=nn!==rn)||(dr.css(Ce,nn+1),rn=mr[Hi],dr.css(Ce,tn),Q=nn!==rn)}var en=(Z||Q)&&C&&!t,on=f(en,kr),an=!en&&kr,un=!(!Yt||!C||t)&&0===Ii.round(U.bottom-U.top),fn=f(un,Cr),cn=!un&&Cr,ln="-"+Ce,sn=en&&Xt||!Xt,vn=un&&Xt||!Xt,dn={c:o,t:vn?yi(ar.css(xe+_e+ln),!0):0,r:sn?yi(ar.css(xe+Se+ln),!0):0,b:vn?yi(ar.css(xe+ze+ln),!0):0,l:sn?yi(ar.css(xe+Te+ln),!0):0},hn={c:o,t:yi(ar.css(ge+_e)),r:yi(ar.css(ge+Se)),b:yi(ar.css(ge+ze)),l:yi(ar.css(ge+Te))},pn={h:String(ar.css(ke+Ae)),w:String(ar.css(ke+Ce))},bn={},mn={},yn=function(){return{w:mr[Li],h:mr[Ai]}},gn=function(){return{w:gr[Hi]+Ii.max(0,xr[Li]-xr[Ri]),h:gr[Ci]+Ii.max(0,xr[Ai]-xr[Ni])}},wn=$t=K.l+K.r,xn=Kt=K.t+K.b;if(wn*=z?1:0,xn*=z?1:0,K.c=f(K,Hr),Gt=dn.l+dn.r,Jt=dn.t+dn.b,dn.c=f(dn,Lr),Qt=hn.l+hn.r,Zt=hn.t+hn.b,hn.c=f(hn,Rr),pn.ih=yi(pn.h),pn.iw=yi(pn.w),pn.ch=-1<pn.h.indexOf("px"),pn.cw=-1<pn.w.indexOf("px"),pn.c=f(pn,Ar),ee=t,Jr=q,Dr=B,Nr=Y,kr=en,Cr=un,Hr=K,Lr=dn,Rr=hn,Ar=pn,X&&Yt&&ur.css(Ne,J),K.c||X||T||on||fn||$||A){var _n={},Sn={};mi(mn,ge,[-K.t,-K.r,-K.b,-K.l]),z?(mi(_n,He,[K.t,K.r,K.b,K.l]),mi(Vt?Sn:bn,we)):(mi(_n,He),mi(Vt?Sn:bn,we,[K.t,K.r,K.b,K.l])),cr.css(_n),or.css(Sn)}me=gn();var zn=!!Vt&&fi(),Tn=Vt&&f(zn,Zr),On=Vt&&zn?{w:M?zn.B:zn.q,h:j?zn.X:zn.V}:{};if(Zr=zn,un&&(fn||T||$||pn.c||K.c||dn.c)?bn[Ae]=Le:(fn||T)&&(bn[ke+Ae]=He,bn[Ae]=De),en&&(on||T||$||pn.c||K.c||dn.c||X)?(bn[Ce]=Le,mn[ke+Ce]=De):(on||T)&&(bn[ke+Ce]=He,bn[Ce]=De,bn[Ne]=He,mn[ke+Ce]=He),en?(pn.cw||(bn[ke+Ce]=He),mn[Ce]=Le,bn[Ce]=Le,bn[Ne]=J):mn[Ce]=He,un?(pn.ch||(bn[ke+Ae]=He),mn[Ae]=On.h||xr[Ai]):mn[Ae]=He,C&&dr.css(mn),sr.css(bn),bn={},mn={},e||i||Tn||X||$||T||on||en||fn||un||pn.c||H||I||k||S||d||p||g||m||W||E||V){var kn="overflow",Cn=kn+"-x",An=kn+"-y",Nn=Ut?Rt.x||Rt.y||me.w<ye.y||me.h<ye.x||un||P:un,Hn={},Ln=Tr.y&&Or.ys&&!N&&!Wt?Rt.y?lr.css(G):-Et.y:0,Rn=Tr.x&&Or.xs&&!N&&!Wt?Rt.x?lr.css(ze):-Et.x:0;mi(Hn,He),lr.css(Hn),Nn&&sr.css(kn,"hidden");var Dn=gi(),In=Ut&&!Nn?wr:Dn,Mn={w:On.w||Dn[Li],h:On.h||Dn[Ai]},Wn=Ii.max(Dn[Ri],In[Ri]),jn=Ii.max(Dn[Ni],In[Ni]);Hn[ze]=cn?He:Rn,Hn[G]=an?He:Ln,lr.css(Hn),me=gn();var En=yn(),Fn={w:Ii.max((en?Mn.w:Wn)+wn,En.w),h:Ii.max((un?Mn.h:jn)+xn,En.h)};if(Fn.c=f(Fn,Wr),Wr=Fn,C){(Fn.c||un||en)&&(mn[Ce]=Fn.w,mn[Ae]=Fn.h,Vt||(Mn={w:Dn[Li],h:Dn[Ai]}));var Un={},Pn=function(n){var t=pi(n),r=t.F,e=t.Y,i=n?en:un,o=n?Gt:Jt,a=n?$t:Kt,u=n?Qt:Zt,f=mn[e]+(Xt?o:-a);i&&(i||!dn.c)||(mn[e]=En[r]-(Xt?0:a+o)-1-u),i&&pn["c"+r]&&pn["i"+r]===f&&(mn[e]=f+(Xt?0:a)+1),!(i&&Mn[r]<me[r])||n&&Vt&&q||(Vt&&(Un[e]=yi(hr.css(e))-1),mn[e]-=1),0<Mn[r]&&(mn[e]=Ii.max(1,mn[e]))};Pn(!0),Pn(!1),Vt&&hr.css(Un),dr.css(mn)}en&&(bn[Ce]=De),!en||Xt||ie||(bn[Ne]="none"),sr.css(bn),bn={};var qn={w:Ii.max(Dn[Ri],In[Ri]),h:Ii.max(Dn[Ni],In[Ni])};qn.c=i=f(qn,Sr),Sr=qn,Nn&&sr.css(kn,He),me=gn(),e=f(En=yn(),_r),_r=En;var Vn=Vt&&(0===me.w||0===me.h),Bn=Er,Xn={},Yn={},$n={},Kn={},Gn={},Jn={},Qn={},Zn=gr.getBoundingClientRect(),nt=function(n){var t=pi(n),r=pi(!n).P,e=t.P,i=t.F,o=t.Y,a=Re+t.$+"Max",u=Zn[o]?Ii.abs(Zn[o]-me[i]):0;Xn[e]="v-s"===D[e],Yn[e]="v-h"===D[e],$n[e]="s"===D[e],Kn[e]=Ii.max(0,Ii.round(100*(qn[i]-me[i]))/100),Kn[e]*=Vn||0===wr[a]&&0<u&&u<1?0:1,Gn[e]=0<Kn[e],Jn[e]=Xn[e]||Yn[e]?Gn[r]&&!Xn[r]&&!Yn[r]:Gn[e],Jn[e+"s"]=!!Jn[e]&&($n[e]||Xn[e]),Qn[e]=Gn[e]&&Jn[e+"s"]};if(nt(!0),nt(!1),Kn.c=f(Kn,Er),Er=Kn,Gn.c=f(Gn,Tr),Tr=Gn,Jn.c=f(Jn,Or),Or=Jn,Rt.x||Rt.y){var tt,rt={},et={},it=o;(Gn.x||Gn.y)&&(et.w=Rt.y&&Gn.y?qn.w+Dt.y:He,et.h=Rt.x&&Gn.x?qn.h+Dt.x:He,it=f(et,zr),zr=et),(Gn.c||Jn.c||qn.c||X||on||fn||en||un||H)&&(bn[ge+J]=bn[xe+J]=He,tt=function(n){var t=pi(n),r=pi(!n),e=t.P,i=n?ze:G,o=n?un:en;Rt[e]&&Gn[e]&&Jn[e+"s"]?(bn[ge+i]=o?N?He:Dt[e]:He,bn[xe+i]=n&&o||N?He:Dt[e]+"px solid transparent"):(et[r.F]=bn[ge+i]=bn[xe+i]=He,it=!0)},Wt?N?zi(lr,Be):Si(lr,Be):(tt(!0),tt(!1))),N&&(et.w=et.h=He,it=!0),it&&!Wt&&(rt[Ce]=Jn.y?et.w:He,rt[Ae]=Jn.x?et.h:He,vr||(vr=Mi(wi(Xe)),lr.prepend(vr)),vr.css(rt)),sr.css(bn)}var ot,at={};_n={};if((e||Gn.c||Jn.c||qn.c||I||$||H||X||k||fn)&&(at[J]=He,(ot=function(n){function t(){at[a]=He,be[e.F]=0}var r=pi(n),e=pi(!n),i=r.P,o=r.K,a=n?ze:G;Gn[i]&&Jn[i+"s"]?(at[kn+o]=Re,N||Wt?t():(at[a]=-(Rt[i]?Dt[i]:Et[i]),be[e.F]=Rt[i]?Dt[e.P]:0)):(at[kn+o]=He,t())})(!0),ot(!1),!Wt&&(me.h<ye.x||me.w<ye.y)&&(Gn.x&&Jn.x&&!Rt.x||Gn.y&&Jn.y&&!Rt.y)?(at[we+_e]=ye.x,at[ge+_e]=-ye.x,at[we+J]=ye.y,at[ge+J]=-ye.y):at[we+_e]=at[ge+_e]=at[we+J]=at[ge+J]=He,at[we+G]=at[ge+G]=He,Gn.x&&Jn.x||Gn.y&&Jn.y||Vn?Vt&&Vn&&(_n[Cn]=_n[An]="hidden"):(!O||Yn.x||Xn.x||Yn.y||Xn.y)&&(Vt&&(_n[Cn]=_n[An]=He),at[Cn]=at[An]="visible"),cr.css(_n),lr.css(at),at={},(Gn.c||$||on||fn)&&(!Rt.x||!Rt.y))){var ut=xr[Oi];ut.webkitTransform="scale(1)",ut.display="run-in",xr[Ci],ut.display=He,ut.webkitTransform=He}if(bn={},X||on||fn)if(nr&&en){var ft=sr.css(Ne),ct=Ii.round(sr.css(Ne,He).css(Te,He).position().left);sr.css(Ne,ft),ct!==Ii.round(sr.position().left)&&(bn[Te]=ct)}else bn[Te]=He;if(sr.css(bn),Vt&&i){var lt=function At(){var n=br.selectionStart;if(n===Ti)return;var t,r,e=or.val(),i=e[ki],o=e.split("\n"),a=o[ki],u=e.substr(0,n).split("\n"),f=0,c=0,l=u[ki],s=u[u[ki]-1][ki];for(r=0;r<o[ki];r++)t=o[r][ki],c<t&&(f=r+1,c=t);return{G:l,J:s,Q:a,Z:c,nn:f,tn:n,rn:i}}();if(lt){var st=Qr===Ti||lt.Q!==Qr.Q,vt=lt.G,dt=lt.J,ht=lt.nn,pt=lt.Q,bt=lt.Z,mt=lt.tn,yt=lt.rn<=mt&&oe,gt={x:q||dt!==bt||vt!==ht?-1:Er.x,y:(q?yt||st&&Bn!==Ti&&c.y===Bn.y:(yt||st)&&vt===pt)?Er.y:-1};c.x=-1<gt.x?nr&&$r&&It.i?0:gt.x:c.x,c.y=-1<gt.y?gt.y:c.y}Qr=lt}nr&&It.i&&Rt.y&&Gn.x&&$r&&(c.x+=be.w||0),en&&ar[Me](0),un&&ar[We](0),lr[Me](c.x)[We](c.y);var wt="v"===v,xt="h"===v,_t="a"===v,St=Di.bind(li,0,!0,!0,Qn.x),zt=Di.bind(li,0,!1,!0,Qn.y),Tt=Di.bind(li,0,!0,!1,Qn.x),Ot=Di.bind(li,0,!1,!1,Qn.y);if(Jn.x||Jn.y?Si(ar,Pe):zi(ar,Pe),Jn.x?Si(ar,qe):zi(ar,qe),Jn.y?Si(ar,Ve):zi(ar,Ve),X&&(nr?Si(ar,Ee):zi(ar,Ee)),Bt&&Si(ar,Fe),S){var kt=function(n){Ft?ri(pr,je,pe,n,!0):pr[n?"off":"on"](je,pe)};zi(pr,[Ke,Ge,Je,Qe].join(Ie)),se?(Si(ar,Fe),kt(!0)):(zi(ar,Fe),Si(pr,Ke),ve?Si(pr,Ge):de?Si(pr,Je):he&&Si(pr,Qe),kt(!0),kt())}(d||I||Jn.c||Gn.c||H)&&(N?H&&(zi(ar,Ue),N&&(Tt(),Ot())):_t?(Qn.x?St():Tt(),Qn.y?zt():Ot()):wt?(St(),zt()):xt&&(Tt(),Ot())),(p||H)&&(le||ce?(ai(!0),ai()):ai(!0),ue?si(!0):si(!1,!0)),(e||Kn.c||fn||on||S||$||T||H||X)&&(vi(!0),di(!0),vi(!1),di(!1)),m&&hi(!0,b),g&&hi(!1,y),X&&bi("onDirectionChanged",{isRTL:nr,dir:B}),e&&bi("onHostSizeChanged",{width:_r.w,height:_r.h}),i&&bi("onContentSizeChanged",{width:Sr.w,height:Sr.h}),(Gn.c||Jn.c)&&bi("onOverflowChanged",{x:Gn.x,y:Gn.y,xScrollable:Jn.xs,yScrollable:Jn.ys,clipped:Jn.x||Jn.y}),Kn.c&&bi("onOverflowAmountChanged",{x:Kn.x,y:Kn.y})}Bt&&re&&(Tr.c||re.c)&&(re.f||ui(),Rt.y&&Tr.x&&sr.css(Oe+Ce,re.w+Dt.y),Rt.x&&Tr.y&&sr.css(Oe+Ae,re.h+Dt.x),re.c=!1),bi("onUpdated",{forced:o})}}function Qn(n){var t=ht._(n,ht.g,!0,a);return a=_i({},a,t.S),rr=_i({},rr,t.z),t.z}function Zn(e){function n(){var r=e?or:ar;s(i,function(n,t){ln(t)==yt&&(n==xt?r.addClass(t):r.attr(n,t))})}var t=rr.textarea.inheritedAttrs,i={},r=[en,on,Fe,Ee,an,un,fn,Ue,Pe,qe,Ve,Ze,cn,On,Kr].join(Ie);if(t=ln(t)==yt?t.split(" "):t,ln(t)==mt&&s(t,function(n,t){ln(t)==yt&&(i[t]=e?ar.attr(t):or.attr(t))}),e)sr.contents().unwrap().unwrap().unwrap(),zi(ar,r),Vt?(or.removeAttr(Oi),u&&n(),zi(or,r),ft(hr),u?(or.unwrap(),ft(ar)):Si(ar,on)):zi(or,en),Bt&&zi(v,rn),ft(fr);else{if(Vt){var o={},a=or.parent();u=!(a.hasClass(on)&&1===a.children()[ki]),rr.sizeAutoCapable||(o[Ce]=or.css(Ce),o[Ae]=or.css(Ae)),u&&or.wrap(wi(on)),(ar=or.parent()).css(o).wrapInner(wi(_n+Ie+On)).wrapInner(wi(wn+Ie+On)).wrapInner(wi(gn+Ie+On)),sr=ct(ar,P+_n),lr=ct(ar,P+wn),cr=ct(ar,P+gn),hr=Mi(wi(yn)),sr.prepend(hr),Si(or,cn+Ie+On),u&&n()}else(ar=or).wrapInner(wi(_n)).wrapInner(wi(wn)).wrapInner(wi(gn)),sr=ct(ar,P+_n),lr=ct(ar,P+wn),cr=ct(ar,P+gn),Si(or,en);Wt&&Si(lr,Be),Rt.x&&Rt.y&&Si(lr,xn),Bt&&Si(v,rn),fr=Mi(wi("os-resize-observer-host")),ar.prepend(fr),H=fr[0],mr=ar[0],gr=cr[0],wr=lr[0],xr=sr[0]}}function nt(n){var r,t,e,i,o=[112,113,114,115,116,117,118,119,120,121,123,33,34,37,38,39,40,16,17,18,19,20,144],a=[],u=n?"off":"on";!n&&Vt&&(e=function(n){fi(),Lt.update(Le),n&&clearInterval(r)},(k={})[Re]=function(n){return or[Me](It.i&&$r?9999999:0),or[We](0),Di.prvD(n),Di.stpP(n),!1},k.drop=function(){setTimeout(function(){qt||e()},50)},k.focus=function(){oe=!0},k.focusout=function(){e(!(oe=!(a=[])))},9<M||!Mt?k.input=function(){e()}:(k[G]=function(n){var t=n.keyCode;-1<sn(t,o)||(a.length||(e(),r=setInterval(e,1e3/60)),-1===sn(t,a)&&a.push(t))},k[J]=function(n){var t=n.keyCode,r=sn(t,a);-1<sn(t,o)||(-1<r&&a.splice(r,1),a.length||e(!0))})),Vt?s(k,function(n,t){or[u](n,t)}):sr[u](Z,function(n){!0!==Ur&&function s(n){if(!Pt)return!0;function t(n,t){for(var r=0;r<n[ki];r++)if(n[r]===t)return!0;return!1}var r="flex-grow",e="flex-shrink",i="flex-basis",o=[Ce,Oe+Ce,ke+Ce,ge+Te,ge+Se,Te,Se,"font-weight","word-spacing",r,e,i],a=[we+Te,we+Se,xe+Te+Ce,xe+Se+Ce],u=[Ae,Oe+Ae,ke+Ae,ge+_e,ge+ze,_e,ze,"line-height",r,e,i],f=[we+_e,we+ze,xe+_e+Ce,xe+ze+Ce],c="s"===jr.x||"v-s"===jr.x,l=!1;return("s"===jr.y||"v-s"===jr.y)&&((l=t(u,n))||Xt||(l=t(f,n))),c&&!l&&((l=t(o,n))||Xt||(l=t(a,n))),l}((n=n.originalEvent||n).propertyName)&&Lt.update(Le)}),n||(i=function(n){tr||(t!==Ti?clearTimeout(t):((fe||ce)&&si(!0),at()||Si(ar,Ue),bi("onScrollStart",n)),U||(di(!0),di(!1)),bi("onScroll",n),t=setTimeout(function(){qt||(clearTimeout(t),t=Ti,(fe||ce)&&si(!1),at()||zi(ar,Ue),bi("onScrollStop",n))},175))},Ft?ri(lr,Re,i):lr.on(Re,i))}function tt(n){n?(ft(c),ft(p)):(c=Mi(wi(kn+Ie+In)),d=Mi(wi(Cn)),h=Mi(wi(Nn)),p=Mi(wi(kn+Ie+Mn)),b=Mi(wi(Cn)),m=Mi(wi(Nn)),c.append(d),d.append(h),p.append(b),b.append(m),cr.after(p),cr.after(c))}function rt(_){var S,i,z,T,r=pi(_),O=r.en,t=g.top!==g,k=r.P,e=r.K,C=Re+r.$,o="active",a="snapHandle",A=1,u=[16,17];function f(n,t,r){var e=0;if(ln(t)==mt&&ln(r)==mt)for(;e<t[ki];e++)f(n,t[e],r[e]);else Ft?ri(n,t,r,!1,!0):n.on(t,r)}function c(n){return M&&t?n["screen"+e]:Di.page(n)[k]}function l(n){return rr.scrollbars[n]}function s(){A=.5}function v(){A=1}function N(n){-1<sn(n.keyCode,u)&&s()}function H(n){-1<sn(n.keyCode,u)&&v()}function L(n){var t=(n.originalEvent||n).touches!==Ti;return!(tr||qt||at()||!Xr||t&&!l("touchSupport"))&&(1===Di.mBtn(n)||t)}function d(n){if(L(n)){var t=O.W,r=O.I,e=O.R*((c(n)-z)*T/(t-r));e=isFinite(e)?e:0,nr&&_&&!It.i&&(e*=-1),lr[C](Ii.round(i+e)),U&&di(_,i+e),Ft||Di.prvD(n)}else R(n)}function R(n){if(n=n||n.originalEvent,j.off(Y,d).off(X,R).off(G,N).off(J,H).off(Q,Kn),U&&di(_,!0),U=!1,zi(E,Wn),zi(r["in"],o),zi(r.an,o),zi(r.un,o),z=i=Ti,T=1,v(),S!==Ti&&(Lt.scrollStop(),clearTimeout(S),S=Ti),n){var t=mr.getBoundingClientRect();n.clientX>=t.left&&n.clientX<=t.right&&n.clientY>=t.top&&n.clientY<=t.bottom||Yn(),(fe||ce)&&si(!1)}}function D(n){i=lr[C](),i=isNaN(i)?0:i,(nr&&_&&!It.n||!nr)&&(i=i<0?0:i),T=it()[k],z=c(n),U=!l(a),Si(E,Wn),Si(r["in"],o),Si(r.un,o),j.on(Y,d).on(X,R).on(Q,Kn),!M&&y||Di.prvD(n),Di.stpP(n)}f(r["in"],je,function h(n){L(n)&&D(n)}),f(r.an,[je,$,K],[function I(n){if(L(n)){var d,h=Ii.round(me[r.F]),p=r.an.offset()[r.U],t=n.ctrlKey,b=n.shiftKey,m=b&&t,y=!0,g=function(n){U&&di(_,n)},w=function(){g(),D(n)},x=function(){if(!qt){var n=(z-p)*T,t=O.D,r=O.W,e=O.I,i=O.R,o=O.H,a=270*A,u=y?Ii.max(400,a):a,f=i*((n-e/2)/(r-e)),c=nr&&_&&(!It.i&&!It.n||$r),l=c?t<n:n<t,s={},v={easing:"linear",step:function(n){U&&(lr[C](n),di(_,n))}};f=isFinite(f)?f:0,f=nr&&_&&!It.i?i-f:f,b?(lr[C](f),m?(f=lr[C](),lr[C](o),f=c&&It.i?i-f:f,f=c&&It.n?-f:f,s[k]=f,Lt.scroll(s,_i(v,{duration:130,complete:w}))):w()):(d=y?l:d,(c?d?n<=t+e:t<=n:d?t<=n:n<=t+e)?(clearTimeout(S),Lt.scrollStop(),S=Ti,g(!0)):(S=setTimeout(x,u),s[k]=(d?"-=":"+=")+h,Lt.scroll(s,_i(v,{duration:a}))),y=!1)}};t&&s(),T=it()[k],z=Di.page(n)[k],U=!l(a),Si(E,Wn),Si(r.an,o),Si(r.un,o),j.on(X,R).on(G,N).on(J,H).on(Q,Kn),x(),Di.prvD(n),Di.stpP(n)}},function p(n){F=!0,(fe||ce)&&si(!0)},function b(n){F=!1,(fe||ce)&&si(!1)}]),f(r.un,je,function m(n){Di.stpP(n)}),W&&r.un.on(Z,function(n){n.target===r.un[0]&&(vi(_),di(_))})}function li(n,t,r){var e=n?an:un,i=n?c:p;t?zi(ar,e):Si(ar,e),r?zi(i,Ln):Si(i,Ln)}function si(n,t){if(clearTimeout(C),n)zi(c,Rn),zi(p,Rn);else{var r,e=function(){F||qt||(!(r=h.hasClass("active")||m.hasClass("active"))&&(fe||ce||le)&&Si(c,Rn),!r&&(fe||ce||le)&&Si(p,Rn))};0<ae&&!0!==t?C=setTimeout(e,ae):e()}}function vi(n){var t={},r=pi(n),e=r.en,i=Ii.min(1,(_r[r.F]-(Ir?n?$t:Kt:0))/Sr[r.F]);t[r.Y]=Ii.floor(100*i*1e6)/1e6+"%",at()||r["in"].css(t),e.I=r["in"][0]["offset"+r.cn],e.M=i}function di(n,t){function r(n){return isNaN(n/g)?0:Ii.max(0,Ii.min(1,n/g))}function e(n){var t=m*n;return t=isNaN(t)?0:t,t=u&&!It.i?b-p-t:t,t=Ii.max(0,t)}var i,o,a=ln(t)==gt,u=nr&&n,f=pi(n),c=f.en,l="translate(",s=Tt.v("transform"),v=Tt.v("transition"),d=n?lr[Me]():lr[We](),h=t===Ti||a?d:t,p=c.I,b=f.an[0]["offset"+f.cn],m=b-p,y={},g=(wr[Re+f.cn]-wr["client"+f.cn])*(It.n&&u?-1:1),w=r(d),x=e(r(h)),_=e(w);c.R=g,c.H=d,c.L=w,vn?(i=u?-(b-p-x):x,o=n?l+i+"px, 0)":l+"0, "+i+"px)",y[s]=o,W&&(y[v]=a&&1<Ii.abs(x-c.D)?function S(n){var t=Tt.v("transition"),r=n.css(t);if(r)return r;for(var e,i,o,a="\\s*(([^,(]+(\\(.+?\\))?)+)[\\s,]*",u=new RegExp(a),f=new RegExp("^("+a+")+$"),c="property duration timing-function delay".split(" "),l=[],s=0,v=function(n){if(e=[],!n.match(f))return n;for(;n.match(u);)e.push(RegExp.$1),n=n.replace(u,He);return e};s<c[ki];s++)for(i=v(n.css(t+"-"+c[s])),o=0;o<i[ki];o++)l[o]=(l[o]?l[o]+Ie:He)+i[o];return l.join(", ")}(f["in"])+", "+(s+Ie+250)+"ms":He)):y[f.U]=x,at()||(f["in"].css(y),vn&&W&&a&&f["in"].one(Z,function(){qt||f["in"].css(v,He)})),c.D=x,c.j=_,c.W=b}function hi(n,t){var r=t?"removeClass":"addClass",e=n?b:m,i=n?An:Hn;(n?d:h)[r](i),e[r](i)}function pi(n){return{Y:n?Ce:Ae,cn:n?"Width":"Height",U:n?Te:_e,$:n?"Left":"Top",P:n?bn:mn,K:n?"X":"Y",F:n?"w":"h",ln:n?"l":"t",an:n?d:b,"in":n?h:m,un:n?c:p,en:n?dn:hn}}function et(n){n?ft(pr):(pr=Mi(wi(Dn)),ar.append(pr))}function bi(n,t){if(Pt){var r,e=rr.callbacks[n],i=n;"on"===i.substr(0,2)&&(i=i.substr(2,1).toLowerCase()+i.substr(3)),ln(e)==bt&&e.call(Lt,t),s(En,function(){ln((r=this).on)==bt&&r.on(i,t)})}else qt||jn.push({n:n,a:t})}function mi(n,t,r){r===Ti&&(r=[He,He,He,He]),n[t+_e]=r[0],n[t+Se]=r[1],n[t+ze]=r[2],n[t+Te]=r[3]}function it(){var n=gr.getBoundingClientRect();return{x:vn&&1/(Ii.round(n.width)/gr[Hi])||1,y:vn&&1/(Ii.round(n.height)/gr[Ci])||1}}function ot(n){var t="ownerDocument",r="HTMLElement",e=n&&n[t]&&n[t].parentWindow||st;return typeof e[r]==pt?n instanceof e[r]:n&&typeof n==pt&&null!==n&&1===n.nodeType&&typeof n.nodeName==yt}function yi(n,t){var r=t?parseFloat(n):parseInt(n,10);return isNaN(r)?0:r}function at(){return Fr&&Rt.x&&Rt.y}function gi(){return Vt?hr[0]:xr}function wi(r,n){return"<div "+(r?ln(r)==yt?'class="'+r+'"':function(){var n,t="";if(Mi.isPlainObject(r))for(n in r)t+=("className"===n?"class":n)+'="'+r[n]+'" ';return t}():He)+">"+(n||He)+"</div>"}function ut(n,t){for(var r,e=t.split(P),i=0;i<e.length;i++){if(!n[zt](e[i]))return;r=n[e[i]],i<e.length&&ln(r)==pt&&(n=r)}return r}function xi(n,t,r){if(r)return r;if(ln(n)!=pt||ln(t)!=pt)return n!==t;for(var e in n)if("c"!==e){if(!n[zt](e)||!t[zt](e))return!0;if(xi(n[e],t[e]))return!0}return!1}function _i(){return Mi.extend.apply(this,[!0].concat([].slice.call(arguments)))}function Si(n,t){return e.addClass.call(n,t)}function zi(n,t){return e.removeClass.call(n,t)}function ft(n){return e.remove.call(n)}function ct(n,t){return e.find.call(n,t).eq(0)}}return Ot&&Ot.fn&&(Ot.fn.overlayScrollbars=function(n,t){return Ot.isPlainObject(n)?(Ot.each(this,function(){v(this,n,t)}),this):v(this,n)}),v});