<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>jsGrid - Simple jQuery DataGrid - Demos</title>
    <link rel="stylesheet" type="text/css" href="demos.css" />
    <link href='http://fonts.googleapis.com/css?family=Open+Sans:300,600,400' rel='stylesheet' type='text/css'>
</head>
<body>
    <div class="navigation">
        <h1>jsGrid Demos</h1>
        <ul>
            <li><a href="basic.html" target="demo">Basic Scenario</a></li>
            <li><a href="static-data.html" target="demo">Static Data</a></li>
            <li><a href="odata-service.html" target="demo">OData Service</a></li>
            <li><a href="data-manipulation.html" target="demo">Data Manipulation</a></li>
            <li><a href="validation.html" target="demo">Validation</a></li>
            <li><a href="sorting.html" target="demo">Sorting</a></li>
            <li><a href="loading-by-page.html" target="demo">Loading by Page</a></li>
            <li><a href="custom-view.html" target="demo">Custom View</a></li>
            <li><a href="custom-row-renderer.html" target="demo">Custom Row Renderer</a></li>
            <li><a href="external-pager.html" target="demo">External Pager</a></li>
            <li><a href="custom-grid-field.html" target="demo">Custom Grid Field</a></li>
            <li><a href="localization.html" target="demo">Localization</a></li>
        </ul>
    </div>
    <div class="demo-frame">
        <iframe name="demo" src="basic.html"></iframe>
    </div>
</body>
</html>