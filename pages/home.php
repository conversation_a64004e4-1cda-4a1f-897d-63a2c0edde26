<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Sakura Panel</title>
    <link href="https://fonts.loli.net/css?family=Oxygen:400,700|Arimo:400,700" rel="stylesheet">
    <link rel="stylesheet" href="assets/home/<USER>/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/scrollreveal@4.0.0/dist/scrollreveal.min.js"></script>
</head>
<body class="is-boxed has-animations">
    <div class="body-wrap boxed-container">
        <header class="site-header">
            <div class="container">
                <div class="site-header-inner">
                    <div class="brand header-brand">
                        <h1 class="m-0">
                            <a href="#">
                                <img src="assets/home/<USER>/images/logo.svg" alt="Blue logo">
                            </a>
                        </h1>
                    </div>
                </div>
            </div>
        </header>

        <main>
            <section class="hero">
				<div class="hero-left-decoration is-revealing"></div>
				<div class="hero-right-decoration is-revealing"></div>
                <div class="container">
                    <div class="hero-inner">
						<div class="hero-copy">
	                        <h1 class="hero-title mt-0 is-revealing">Sakura Panel</h1>
	                        <p class="hero-paragraph is-revealing">免费的 Frp 内网穿透管理面板</p>
	                        <p class="hero-cta mb-0 is-revealing">
								<a class="button button-primary button-shadow" href="?page=login">立即开始使用</a>
							</p>
						</div>
						<div class="hero-illustration">
						</div>
                    </div>
                </div>
            </section>

            <!--<section class="clients section">
                <div class="container">
                    <div class="clients-inner section-inner has-top-divider">
                        <div class="container-sm">
                            <ul class="list-reset mb-0">
                                <li class="is-revealing">
                                    <img src="assets/home/<USER>/images/facebook-logo.svg" alt="Facebook logo">
                                </li>
                                <li class="is-revealing">
                                    <img src="assets/home/<USER>/images/tinder-logo.svg" alt="Tinder logo">
                                </li>
                                <li class="is-revealing">
                                    <img src="assets/home/<USER>/images/airbnb-logo.svg" alt="Airbnb logo">
                                </li>
                                <li class="is-revealing">
                                    <img src="assets/home/<USER>/images/microsoft-logo.svg" alt="Microsoft logo">
                                </li>
								<li class="is-revealing">
									<img src="assets/home/<USER>/images/hubspot-logo.svg" alt="Hubspot logo">
								</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>-->

			<section class="features section text-center">
                <div class="container">
                    <div class="features-inner section-inner has-top-divider">
						<div class="features-header text-center">
							<div class="container-sm">
								<h2 class="section-title mt-0">为什么选择 Sakura Panel</h2>
								<p class="section-paragraph mb-0">这是一款轻快、功能强大的内网穿透面板</p>
							</div>
						</div>
                        <div class="features-wrap">
                            <div class="feature is-revealing">
                                <div class="feature-inner">
                                    <div class="feature-icon">
                                        <img src="assets/home/<USER>/images/feature-icon-01.svg" alt="Feature 01">
                                    </div>
                                    <h4 class="feature-title">多功能</h4>
                                    <p class="text-sm">支持多用户、多节点、流量控制、访问控制等功能，可随意定制</p>
                                </div>
                            </div>
                            <div class="feature is-revealing">
                                <div class="feature-inner">
                                    <div class="feature-icon">
                                        <img src="assets/home/<USER>/images/feature-icon-02.svg" alt="Feature 02">
                                    </div>
                                    <h4 class="feature-title">更方便</h4>
                                    <p class="text-sm">所有的功能都是为了简洁而设计，没有复杂的操作，使用更简单</p>
                                </div>
                            </div>
                            <div class="feature is-revealing">
                                <div class="feature-inner">
                                    <div class="feature-icon">
                                        <img src="assets/home/<USER>/images/feature-icon-03.svg" alt="Feature 03">
                                    </div>
                                    <h4 class="feature-title">更美观</h4>
                                    <p class="text-sm">管理面板基于 AdminLTE 3，美观大气，赏心悦目，颜值丝毫不输</p>
                                </div>
                            </div>
                            <div class="feature is-revealing">
                                <div class="feature-inner">
                                    <div class="feature-icon">
                                        <img src="assets/home/<USER>/images/feature-icon-04.svg" alt="Feature 04">
                                    </div>
                                    <h4 class="feature-title">高性能</h4>
                                    <p class="text-sm">整个系统采用前后端分离设计，后端对接 Frps 独立处理数据</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

			<section class="testimonials section">
				<div class="container">
					<div class="testimonials-inner section-inner">
						<div class="testimonials-header text-center text-light">
							<h2 class="section-title mt-0">用户评价</h2>
						</div>
						<div class="testimonials-wrap">
							<div class="testimonial text-sm is-revealing">
								<div class="testimonial-inner">
									<div class="testimonial-main">
										<div class="testimonial-body">
											<p>I’ve read a ton of blog posts about how some startups hit this milestone with ease. They came up with an amazing idea that “just clicked”, made!</p>
										</div>
									</div>
									<div class="testimonial-footer">
										<div class="testimonial-name"><a href="#">@mikesmith</a></div>
									</div>
								</div>
							</div>
							<div class="testimonial text-sm is-revealing">
								<div class="testimonial-inner">
									<div class="testimonial-main">
										<div class="testimonial-body">
											<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Eleifend donec pretium vulputate sapien. Quisque egestas diam in arcu cursus euismod quis.</p>
										</div>
									</div>
									<div class="testimonial-footer">
										<div class="testimonial-name"><a href="#">@mikesmith</a></div>
									</div>
								</div>
							</div>
							<div class="testimonial text-sm is-revealing">
								<div class="testimonial-inner">
									<div class="testimonial-main">
										<div class="testimonial-body">
											<p>I’ve read a ton of blog posts about how some startups hit this milestone with ease. They came up with an amazing idea that “just clicked”, made!</p>
										</div>
									</div>
									<div class="testimonial-footer">
										<div class="testimonial-name"><a href="#">@mikesmith</a></div>
									</div>
								</div>
							</div>
							<div class="testimonial text-sm is-revealing">
								<div class="testimonial-inner">
									<div class="testimonial-main">
										<div class="testimonial-body">
											<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Eleifend donec pretium vulputate sapien. Quisque egestas diam in arcu cursus euismod quis.</p>
										</div>
									</div>
									<div class="testimonial-footer">
										<div class="testimonial-name"><a href="#">@mikesmith</a></div>
									</div>
								</div>
							</div>
							<div class="testimonial text-sm is-revealing">
								<div class="testimonial-inner">
									<div class="testimonial-main">
										<div class="testimonial-body">
											<p>I’ve read a ton of blog posts about how some startups hit this milestone with ease. They came up with an amazing idea that “just clicked”, made!</p>
										</div>
									</div>
									<div class="testimonial-footer">
										<div class="testimonial-name"><a href="#">@mikesmith</a></div>
									</div>
								</div>
							</div>
							<div class="testimonial text-sm is-revealing">
								<div class="testimonial-inner">
									<div class="testimonial-main">
										<div class="testimonial-body">
											<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Eleifend donec pretium vulputate sapien. Quisque egestas diam in arcu cursus euismod quis.</p>
										</div>
									</div>
									<div class="testimonial-footer">
										<div class="testimonial-name"><a href="#">@mikesmith</a></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>

            <section class="pricing section">
                <div class="container">
                    <div class="pricing-inner section-inner">
                        <h2 class="section-title mt-0 text-center">价格介绍</h2>
						<div>
							<div class="pricing-tables-wrap">
								<div class="pricing-table is-revealing">
									<div class="pricing-table-inner">
										<div class="pricing-table-main">
											<div class="pricing-table-header mb-24 pb-24">
												<div class="pricing-table-title h4 mt-0 mb-16">个人版</div>
												<div class="pricing-table-price"><span class="pricing-table-price-currency">￥</span><span class="pricing-table-price-amount h1">0</span>/月</div>
											</div>
											<ul class="pricing-table-features list-reset text-xs">
												<li>
													<span>5 条可用隧道数量</span>
												</li>
												<li>
													<span>10GB 月流量</span>
												</li>
												<li>
													<span>支持 TCP/UDP/HTTP/HTTPS 隧道</span>
												</li>
												<li>
													<span>社区技术支持</span>
												</li>
											</ul>
										</div>
										<div class="pricing-table-cta">
											<a class="button button-secondary button-shadow button-block" href="#">立即使用</a>
										</div>
									</div>
								</div>
								<div class="pricing-table is-revealing">
									<div class="pricing-table-inner">
										<div class="pricing-table-main">
											<div class="pricing-table-header mb-24 pb-24">
												<div class="pricing-table-title h4 mt-0 mb-16">商业版</div>
												<div class="pricing-table-price"><span class="pricing-table-price-currency">￥</span><span class="pricing-table-price-amount h1">30</span>/月</div>
											</div>
											<ul class="pricing-table-features list-reset text-xs">
												<li>
													<span>20 条可用隧道数量</span>
												</li>
												<li>
													<span>50GB 月流量</span>
												</li>
												<li>
													<span>支持 TCP/UDP/HTTP/HTTPS 隧道</span>
												</li>
												<li>
													<span>客服专人技术支持</span>
												</li>
											</ul>
										</div>
										<div class="pricing-table-cta">
											<a class="button button-primary button-shadow button-block" href="#">立即购买</a>
										</div>
									</div>
								</div>
							</div>
						</div>
                    </div>
                </div>
            </section>

			<section class="cta section">
				<div class="container">
					<div class="cta-inner section-inner is-revealing">
						<h3 class="section-title mt-0">仍然有疑问？</h3>
						<div class="cta-cta">
							<a class="button button-primary button-shadow" href="#">联系在线客服</a>
						</div>
					</div>
				</div>
			</section>
        </main>

        <footer class="site-footer">
            <div class="container">
                <div class="site-footer-inner">
                    <div class="brand footer-brand">
                        <a href="#">
                            <img src="assets/home/<USER>/images/logo.svg" alt="Blue logo">
                        </a>
                    </div>
                    <ul class="footer-links list-reset">
                        <li>
                            <a href="#">联系我们</a>
                        </li>
                        <li>
                            <a href="#">关于我们</a>
                        </li>
                        <li>
                            <a href="#">常见问题</a>
                        </li>
                        <li>
                            <a href="#">技术支持</a>
                        </li>
                    </ul>
                    <ul class="footer-social-links list-reset">
                        <li>
                            <a href="#">
                                <span class="screen-reader-text">Facebook</span>
                                <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.023 16L6 9H3V6h3V4c0-2.7 1.672-4 4.08-4 1.153 0 2.144.086 2.433.124v2.821h-1.67c-1.31 0-1.563.623-1.563 1.536V6H13l-1 3H9.28v7H6.023z" fill="#FFF"/>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <span class="screen-reader-text">Twitter</span>
                                <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M16 3c-.6.3-1.2.4-1.9.5.7-.4 1.2-1 1.4-1.8-.6.4-1.3.6-2.1.8-.6-.6-1.5-1-2.4-1-1.7 0-3.2 1.5-3.2 3.3 0 .3 0 .5.1.7-2.7-.1-5.2-1.4-6.8-3.4-.3.5-.4 1-.4 1.7 0 1.1.6 2.1 1.5 2.7-.5 0-1-.2-1.5-.4C.7 7.7 1.8 9 3.3 9.3c-.3.1-.6.1-.9.1-.2 0-.4 0-.6-.1.4 1.3 1.6 2.3 3.1 2.3-1.1.9-2.5 1.4-4.1 1.4H0c1.5.9 3.2 1.5 5 1.5 6 0 9.3-5 9.3-9.3v-.4C15 4.3 15.6 3.7 16 3z" fill="#FFF"/>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <span class="screen-reader-text">Google</span>
                                <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.9 7v2.4H12c-.2 1-1.2 3-4 3-2.4 0-4.3-2-4.3-4.4 0-2.4 2-4.4 4.3-4.4 1.4 0 2.3.6 2.8 1.1l1.9-1.8C11.5 1.7 9.9 1 8 1 4.1 1 1 4.1 1 8s3.1 7 7 7c4 0 6.7-2.8 6.7-6.8 0-.5 0-.8-.1-1.2H7.9z" fill="#FFF"/>
                                </svg>
                            </a>
                        </li>
                    </ul>
                    <div class="footer-copyright">&copy; <?php echo date("Y"); ?> Sakura Panel</div>
                </div>
            </div>
        </footer>
    </div>

    <script src="assets/home/<USER>/js/main.min.js"></script>
</body>
</html>
