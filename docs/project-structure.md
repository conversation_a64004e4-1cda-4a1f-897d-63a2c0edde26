# SakuraPanel 项目结构总览

本文档提供了 SakuraPanel 项目的完整目录结构和文件说明。

## 📁 项目目录结构

```
SakuraPanel/
├── README.md                    # 项目主文档
├── index.php                    # 主入口文件
├── configuration.php            # 核心配置文件
├── daemon.php                   # 守护进程
├── gencode.php                  # 邀请码生成工具
├── import.sql                   # 数据库结构文件
├── test.php                     # 临时测试文件
│
├── api/                         # API接口目录
│   └── index.php               # API主入口
│
├── assets/                      # 静态资源目录
│   ├── configuration/          # 配置相关资源
│   ├── download/               # 下载文件目录
│   ├── email/                  # 邮件模板
│   ├── home/                   # 首页资源
│   └── panel/                  # 面板界面资源
│
├── core/                        # 核心类库目录
│   ├── Database.php            # 数据库操作类
│   ├── NodeManager.php         # 节点管理类
│   ├── UserManager.php         # 用户管理类
│   ├── ProxyManager.php        # 隧道管理类
│   ├── PostHandler.php         # POST请求处理器
│   ├── Router.php              # 路由控制器
│   ├── Pages.php               # 页面渲染类
│   ├── Utils.php               # 工具函数类
│   ├── Settings.php            # 系统设置类
│   ├── Smtp.php                # 邮件发送类
│   ├── Regex.php               # 正则验证类
│   ├── Parsedown.php           # Markdown解析器
│   └── PHPMailer/              # 邮件库目录
│
├── docs/                        # 文档目录
│   ├── README.md               # 文档中心首页
│   ├── installation.md         # 安装配置指南
│   ├── api.md                  # API接口文档
│   ├── database.md             # 数据库结构文档
│   ├── troubleshooting.md      # 故障排除指南
│   ├── project-structure.md    # 项目结构文档
│   └── 节点状态检测功能说明.md  # 节点功能文档
│
├── modules/                     # 页面模块目录
│   ├── home.php                # 首页模块
│   ├── nodes.php               # 节点管理模块
│   ├── proxies.php             # 隧道列表模块
│   ├── addproxy.php            # 添加隧道模块
│   ├── traffic.php             # 流量统计模块
│   ├── profile.php             # 用户资料模块
│   ├── settings.php            # 系统设置模块
│   ├── userlist.php            # 用户列表模块
│   ├── sign.php                # 签到模块
│   ├── download.php            # 下载模块
│   ├── configuration.php       # 配置模块
│   └── 404.php                 # 404错误页面
│
├── pages/                       # 页面模板目录
│   ├── login.php               # 登录页面
│   ├── register.php            # 注册页面
│   ├── panel.php               # 主面板框架
│   ├── home.php                # 首页模板
│   ├── findpass.php            # 找回密码页面
│   ├── logout.php              # 退出登录页面
│   ├── 403.php                 # 403错误页面
│   └── 404.php                 # 404错误页面
│
└── tests/                       # 测试文件目录
    ├── README.md               # 测试说明文档
    └── test_node_status.php    # 节点状态测试
```

## 🔧 核心文件说明

### 入口和配置文件

#### index.php
- **作用**: 系统主入口文件
- **功能**: 初始化系统、加载核心类、路由分发
- **重要性**: ⭐⭐⭐⭐⭐

#### configuration.php
- **作用**: 系统核心配置文件
- **包含**: 数据库配置、邮件配置、注册设置等
- **重要性**: ⭐⭐⭐⭐⭐
- **安全**: 需要保护，避免直接访问

#### daemon.php
- **作用**: 命令行守护进程
- **功能**: 流量统计、节点监控、数据同步
- **运行**: 需要在命令行环境运行
- **重要性**: ⭐⭐⭐⭐

### API接口

#### api/index.php
- **作用**: API服务入口
- **功能**: 与FRP服务端通信
- **接口**: 用户认证、配置获取、状态检查等
- **重要性**: ⭐⭐⭐⭐⭐

### 核心类库

#### Database.php
- **作用**: 数据库操作封装
- **功能**: 查询、插入、更新、删除等操作
- **特点**: 支持防SQL注入、错误处理

#### NodeManager.php
- **作用**: 节点管理核心类
- **功能**: 节点CRUD、状态检测、权限验证
- **新增**: 节点状态自动检测功能

#### UserManager.php
- **作用**: 用户管理核心类
- **功能**: 用户认证、注册、权限管理

#### ProxyManager.php
- **作用**: 隧道管理核心类
- **功能**: 隧道CRUD、配置生成、状态管理

## 📚 文档系统

### docs/ 目录
专门的文档目录，包含：
- 安装部署指南
- API接口文档
- 数据库结构说明
- 故障排除指南
- 功能使用说明

### 文档特点
- Markdown格式，易于阅读和维护
- 结构化组织，便于查找
- 包含代码示例和配置说明
- 持续更新，与代码同步

## 🧪 测试系统

### tests/ 目录
包含测试文件和调试工具：
- 功能测试脚本
- 调试工具
- 测试说明文档

### 测试特点
- 独立的测试环境
- 详细的测试输出
- 便于问题排查
- 支持自动化测试

## 🎨 前端资源

### assets/ 目录
静态资源的组织：
- 按功能模块分类
- 包含CSS、JS、图片等
- 支持主题定制
- 优化加载性能

### 页面系统
- **modules/**: 功能模块，处理业务逻辑
- **pages/**: 页面模板，负责界面展示
- 分离关注点，便于维护

## 🔄 系统流程

### 请求处理流程
```
用户请求 → index.php → Router.php → 对应模块 → 页面模板 → 响应
```

### API请求流程
```
FRP服务端 → api/index.php → 验证 → 处理 → JSON响应
```

### 数据流向
```
用户操作 → PostHandler.php → 核心类 → Database.php → MySQL
```

## 📋 文件权限建议

### 安全权限设置
```bash
# 一般文件权限
chmod 644 *.php
chmod 644 *.md
chmod 644 *.sql

# 目录权限
chmod 755 */

# 敏感文件权限
chmod 600 configuration.php
chmod 600 daemon.php

# 可执行文件
chmod 755 gencode.php
```

### 所有者设置
```bash
# Web文件所有者
chown -R www-data:www-data /var/www/html/

# 特殊文件保护
chown root:root configuration.php
```

## 🔧 开发建议

### 添加新功能
1. 在 `core/` 目录添加核心类
2. 在 `modules/` 目录添加页面模块
3. 在 `PostHandler.php` 添加POST处理
4. 更新数据库结构（如需要）
5. 编写相应文档

### 代码规范
- 使用命名空间 `SakuraPanel`
- 遵循PSR-4自动加载规范
- 添加适当的注释和文档
- 进行安全性检查

### 版本控制
- 重要修改前备份数据库
- 使用Git管理代码版本
- 记录更改日志
- 测试后再部署

---

**相关文档**:
- [文档中心首页](README.md)
- [安装配置指南](installation.md)
- [API接口文档](api.md)
