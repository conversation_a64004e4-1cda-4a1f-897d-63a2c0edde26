# SakuraPanel 文档中心

欢迎来到 SakuraPanel 文档中心！这里包含了系统的详细文档和使用指南。

## 📚 文档目录

### 系统文档
- [项目结构说明](../README.md) - 完整的项目结构和功能介绍
- [项目目录结构](project-structure.md) - 详细的目录结构和文件说明
- [安装配置指南](installation.md) - 详细的安装和配置步骤
- [API接口文档](api.md) - 完整的API接口说明
- [数据库结构](database.md) - 数据库表结构和字段说明

### 功能文档
- [节点状态检测功能说明](节点状态检测功能说明.md) - 节点监控和状态管理功能
- [用户管理系统](user-management.md) - 用户注册、登录、权限管理
- [隧道管理系统](proxy-management.md) - 隧道创建、配置、监控
- [流量统计系统](traffic-management.md) - 流量监控和统计功能

### 运维文档
- [部署指南](deployment.md) - 生产环境部署建议
- [安全配置](security.md) - 安全设置和最佳实践
- [性能优化](performance.md) - 系统性能优化建议
- [故障排除](troubleshooting.md) - 常见问题和解决方案

### 开发文档
- [开发指南](development.md) - 开发环境搭建和代码规范
- [扩展开发](extensions.md) - 功能扩展和插件开发
- [测试指南](testing.md) - 测试方法和测试用例
- [更新日志](changelog.md) - 版本更新记录

## 🚀 快速开始

如果您是第一次使用 SakuraPanel，建议按以下顺序阅读文档：

1. **[项目结构说明](../README.md)** - 了解整体架构
2. **[安装配置指南](installation.md)** - 部署系统
3. **[用户管理系统](user-management.md)** - 创建管理员账号
4. **[节点状态检测功能说明](节点状态检测功能说明.md)** - 配置服务器节点

## 📖 文档约定

### 文档格式
- 所有文档使用 Markdown 格式编写
- 代码示例使用语法高亮
- 重要信息使用醒目的标记

### 版本说明
- 文档版本与系统版本保持同步
- 每次功能更新都会更新相应文档
- 过时的文档会标记为已废弃

### 贡献指南
- 欢迎提交文档改进建议
- 发现错误请提交 Issue
- 可以通过 Pull Request 贡献文档

## 🔗 相关链接

- [GitHub 项目地址](https://github.com/ZeroDream-CN/SakuraPanel)
- [SakuraFrp 服务端](https://github.com/ZeroDream-CN/SakuraFrp)
- [在线演示](https://sakuracloud.cn/)

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查阅文档** - 首先查看相关文档
2. **搜索 Issues** - 查看是否有类似问题
3. **提交 Issue** - 详细描述问题和环境
4. **社区讨论** - 加入技术交流群

## 📝 文档更新

- **最后更新**: 2025-07-28
- **文档版本**: v1.0.2
- **维护者**: SakuraPanel 开发团队

---

**注意**: 本文档持续更新中，如有疑问请及时反馈。
