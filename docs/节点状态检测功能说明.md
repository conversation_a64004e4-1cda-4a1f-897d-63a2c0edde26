# 节点状态检测功能说明

## 功能概述

新增的节点状态检测功能可以自动检测FRP服务器节点的连接状态，并提供手动状态控制选项。

## 主要功能

### 1. 自动状态检测
- **实时检测**: 系统会自动检测节点的连接状态
- **响应时间**: 显示节点的响应时间，帮助判断网络质量
- **自动更新**: 页面加载时自动检测，每5分钟自动刷新一次
- **连接超时**: 5秒连接超时，避免长时间等待

### 2. 手动状态控制
管理员可以手动设置节点状态：

- **正常 (200)**: 允许用户使用，系统会自动检测连接状态
- **隐藏 (401)**: 对普通用户隐藏，但管理员可见
- **禁用 (403)**: 禁止用户使用此节点
- **离线 (500)**: 标记为离线状态

### 3. 状态优先级
- 手动设置的"隐藏"和"禁用"状态不会被自动检测覆盖
- 只有"正常"状态的节点会被自动检测并可能更新为"离线"
- 离线的节点如果重新连接成功，会自动恢复为"正常"状态

### 4. 用户界面改进
- **状态指示器**: 不同状态用不同颜色显示
- **响应时间**: 显示节点响应时间，用颜色区分网络质量
  - 绿色: < 100ms (良好)
  - 黄色: 100-500ms (一般)
  - 红色: > 500ms (较差)
- **检测按钮**: 可以单独检测某个节点或批量检测所有节点
- **最后检测时间**: 显示最后一次检测的时间

## 技术实现

### 1. 后端功能 (NodeManager.php)
```php
// 检测单个节点状态
public function checkNodeStatus($nodeId)

// 批量检测所有节点状态  
public function checkAllNodesStatus()

// 更新节点状态
public function updateNodeStatus($nodeId, $status)

// 获取状态文本和CSS类
public function getStatusText($statusCode)
public function getStatusClass($statusCode)
```

### 2. API接口 (PostHandler.php)
- `checknodestatus`: 检测单个节点状态
- `checkallnodesstatus`: 批量检测所有节点状态

### 3. 前端功能 (nodes.php)
- 自动检测和定时刷新
- 实时状态更新
- 响应时间显示
- 用户交互优化

## 使用方法

### 管理员操作
1. **查看节点状态**: 进入节点管理页面，系统会自动检测所有节点状态
2. **手动检测**: 点击"检测所有节点"按钮或单个节点的"检测"链接
3. **设置节点状态**: 编辑节点时可以手动设置状态
4. **监控响应时间**: 查看响应时间列判断网络质量

### 状态说明
- **正常**: 绿色显示，节点在线且可用
- **离线**: 红色显示，节点无法连接或响应超时
- **禁用**: 黄色显示，管理员手动禁用
- **隐藏**: 灰色显示，对普通用户隐藏

## 配置要求

### 服务器要求
- FRP服务器需要开启管理API
- 管理API端口需要可访问
- 正确配置admin_port和admin_pass

### 网络要求
- Web服务器能够访问FRP服务器的管理端口
- 建议配置防火墙规则，只允许Web服务器访问管理端口

## 故障排除

### 常见问题
1. **节点显示离线但实际在线**
   - 检查admin_port和admin_pass配置是否正确
   - 确认防火墙是否阻止了连接
   - 检查FRP服务器是否开启了管理API

2. **检测超时**
   - 网络延迟过高，可以调整超时时间
   - 检查服务器网络连接

3. **状态不更新**
   - 检查浏览器控制台是否有JavaScript错误
   - 确认CSRF token是否正确

### 调试方法
1. 使用测试文件 `test_node_status.php` 进行调试
2. 检查浏览器开发者工具的网络请求
3. 查看服务器错误日志

## 安全考虑

1. **API访问控制**: 只有管理员可以执行节点状态检测
2. **CSRF保护**: 所有请求都包含CSRF token验证
3. **超时限制**: 设置合理的连接超时时间，避免资源占用
4. **权限验证**: 严格验证用户权限

## 未来改进

1. **历史记录**: 保存节点状态变化历史
2. **告警通知**: 节点离线时发送邮件通知
3. **性能监控**: 记录更详细的性能指标
4. **批量操作**: 支持批量启用/禁用节点
