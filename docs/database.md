# SakuraPanel 数据库结构文档

本文档详细介绍了 SakuraPanel 的数据库结构，包括所有表的字段说明和关系。

## 📊 数据库概览

### 基本信息
- **数据库引擎**: InnoDB
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **表数量**: 8个主要表

### 表关系图
```
users (用户表)
├── tokens (令牌表) - 一对一
├── limits (限速表) - 一对一  
├── proxies (隧道表) - 一对多
└── traffic (流量表) - 一对多

groups (用户组表)
└── users - 一对多

nodes (节点表)  
└── proxies - 一对多

findpass (找回密码表)
└── users - 临时关联
```

## 📋 数据表详细说明

### 1. users (用户表)
存储用户基本信息和账户状态。

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `id` | int | 10 | AUTO_INCREMENT | 用户ID，主键 |
| `username` | varchar | 255 | NOT NULL | 用户名，唯一 |
| `password` | varchar | 255 | NOT NULL | 密码哈希 |
| `email` | varchar | 255 | NOT NULL | 邮箱地址 |
| `group` | varchar | 255 | 'default' | 用户组名称 |
| `traffic` | bigint | 16 | 0 | 总流量配额(MB) |
| `used_traffic` | bigint | 16 | 0 | 已使用流量(MB) |
| `reg_time` | bigint | 16 | NULL | 注册时间戳 |
| `last_login` | bigint | 16 | NULL | 最后登录时间 |
| `status` | int | 1 | 1 | 账户状态(0=禁用,1=正常) |

**索引**:
- PRIMARY KEY (`id`)
- UNIQUE KEY (`username`)
- UNIQUE KEY (`email`)
- KEY (`group`)

### 2. groups (用户组表)
定义不同用户组的权限和配额。

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `id` | int | 10 | AUTO_INCREMENT | 组ID，主键 |
| `name` | varchar | 255 | NOT NULL | 组名称，唯一 |
| `friendly_name` | varchar | 255 | NOT NULL | 显示名称 |
| `traffic` | bigint | 255 | NOT NULL | 流量配额(MB) |
| `proxies` | bigint | 255 | NOT NULL | 隧道数量限制 |
| `inbound` | bigint | 255 | NOT NULL | 上行带宽限制(KB/s) |
| `outbound` | bigint | 255 | NOT NULL | 下行带宽限制(KB/s) |

**默认数据**:
```sql
INSERT INTO `groups` VALUES 
(1, 'default', '默认组', 1024, 5, 1024, 1024),
(2, 'vip1', '青铜VIP', 10240, 10, 2048, 2048),
(3, 'vip2', '黄金VIP', 20480, 15, 3072, 3072),
(4, 'vip3', '钻石VIP', 40960, 20, 4096, 4096);
```

### 3. nodes (节点表)
存储FRP服务器节点信息。

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `id` | int | 10 | AUTO_INCREMENT | 节点ID，主键 |
| `name` | varchar | 255 | NOT NULL | 节点名称 |
| `description` | varchar | 255 | NULL | 节点描述 |
| `hostname` | varchar | 255 | NOT NULL | 主机名或域名 |
| `ip` | varchar | 255 | NOT NULL | IP地址 |
| `port` | int | 5 | NOT NULL | FRP服务端口 |
| `admin_port` | int | 5 | NULL | 管理API端口 |
| `admin_pass` | varchar | 255 | NULL | 管理API密码 |
| `token` | varchar | 255 | NULL | 连接令牌 |
| `group` | varchar | 255 | NULL | 允许的用户组(分号分隔) |
| `status` | varchar | 255 | NOT NULL | 节点状态 |

**状态值说明**:
- `200` - 正常在线
- `401` - 隐藏状态  
- `403` - 禁用状态
- `500` - 离线状态

### 4. proxies (隧道表)
存储用户创建的隧道配置。

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `id` | int | 10 | AUTO_INCREMENT | 隧道ID，主键 |
| `username` | varchar | 255 | NOT NULL | 所属用户 |
| `proxy_name` | varchar | 255 | NOT NULL | 隧道名称 |
| `proxy_type` | varchar | 5 | NOT NULL | 隧道类型 |
| `local_ip` | varchar | 255 | NULL | 本地IP |
| `local_port` | int | 5 | NULL | 本地端口 |
| `use_encryption` | varchar | 5 | NULL | 是否加密 |
| `use_compression` | varchar | 5 | NULL | 是否压缩 |
| `domain` | varchar | 255 | NULL | 自定义域名(JSON) |
| `locations` | varchar | 255 | NULL | 路径配置 |
| `host_header_rewrite` | varchar | 255 | NULL | Host头重写 |
| `http_user` | varchar | 255 | NULL | HTTP认证用户名 |
| `http_pwd` | varchar | 255 | NULL | HTTP认证密码 |
| `subdomain` | varchar | 255 | NULL | 子域名 |
| `remote_port` | int | 5 | NULL | 远程端口 |
| `sk` | varchar | 255 | NULL | STCP/XTCP密钥 |
| `node` | int | 10 | NOT NULL | 所属节点ID |
| `status` | varchar | 255 | NOT NULL | 隧道状态 |

**隧道类型**:
- `tcp` - TCP隧道
- `udp` - UDP隧道  
- `http` - HTTP隧道
- `https` - HTTPS隧道
- `stcp` - 安全TCP隧道
- `xtcp` - P2P TCP隧道

### 5. tokens (令牌表)
存储用户的API认证令牌。

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `id` | int | 10 | AUTO_INCREMENT | 令牌ID，主键 |
| `username` | varchar | 255 | NOT NULL | 所属用户 |
| `token` | varchar | 255 | NOT NULL | 16位令牌字符串 |
| `created_at` | timestamp | - | CURRENT_TIMESTAMP | 创建时间 |
| `last_used` | timestamp | - | NULL | 最后使用时间 |

### 6. limits (限速表)
存储用户个性化限速配置。

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `id` | int | 10 | AUTO_INCREMENT | 限速ID，主键 |
| `username` | varchar | 255 | NOT NULL | 所属用户 |
| `inbound` | bigint | 16 | NOT NULL | 上行限速(KB/s) |
| `outbound` | bigint | 16 | NOT NULL | 下行限速(KB/s) |

### 7. traffic (流量表)
记录用户的流量使用情况。

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `id` | int | 10 | AUTO_INCREMENT | 记录ID，主键 |
| `username` | varchar | 255 | NOT NULL | 所属用户 |
| `node_id` | int | 10 | NOT NULL | 节点ID |
| `proxy_name` | varchar | 255 | NOT NULL | 隧道名称 |
| `inbound` | bigint | 16 | 0 | 上行流量(字节) |
| `outbound` | bigint | 16 | 0 | 下行流量(字节) |
| `date` | date | - | NOT NULL | 统计日期 |
| `created_at` | timestamp | - | CURRENT_TIMESTAMP | 创建时间 |

### 8. findpass (找回密码表)
临时存储密码重置请求。

| 字段名 | 类型 | 长度 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `username` | varchar | 255 | NOT NULL | 用户名 |
| `link` | varchar | 255 | NOT NULL | 重置链接 |
| `time` | bigint | 16 | NOT NULL | 过期时间戳 |

## 🔗 表关系说明

### 用户相关关系
```sql
-- 用户与用户组
users.group -> groups.name

-- 用户与令牌 (一对一)
users.username -> tokens.username

-- 用户与限速 (一对一，可选)
users.username -> limits.username

-- 用户与隧道 (一对多)
users.username -> proxies.username

-- 用户与流量 (一对多)
users.username -> traffic.username
```

### 节点相关关系
```sql
-- 节点与隧道 (一对多)
nodes.id -> proxies.node

-- 节点与流量 (一对多)
nodes.id -> traffic.node_id
```

## 📈 数据统计查询

### 常用统计查询
```sql
-- 用户总数
SELECT COUNT(*) FROM users;

-- 在线节点数
SELECT COUNT(*) FROM nodes WHERE status = '200';

-- 总隧道数
SELECT COUNT(*) FROM proxies;

-- 今日流量统计
SELECT 
    SUM(inbound + outbound) as total_traffic,
    COUNT(DISTINCT username) as active_users
FROM traffic 
WHERE date = CURDATE();

-- 用户组分布
SELECT 
    g.friendly_name,
    COUNT(u.id) as user_count
FROM groups g
LEFT JOIN users u ON g.name = u.group
GROUP BY g.id;
```

### 性能优化查询
```sql
-- 添加索引
ALTER TABLE traffic ADD INDEX idx_date_user (date, username);
ALTER TABLE proxies ADD INDEX idx_user_node (username, node);
ALTER TABLE tokens ADD INDEX idx_token (token);

-- 清理过期数据
DELETE FROM findpass WHERE time < UNIX_TIMESTAMP() - 3600;
DELETE FROM traffic WHERE date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
```

## 🛠️ 维护建议

### 定期维护
1. **清理过期数据**: 定期清理过期的找回密码记录
2. **流量数据归档**: 定期归档旧的流量数据
3. **索引优化**: 根据查询模式优化索引
4. **数据备份**: 定期备份重要数据

### 监控指标
- 用户增长趋势
- 流量使用情况  
- 节点性能状态
- 隧道使用分布

---

**相关文档**:
- [安装配置指南](installation.md)
- [API接口文档](api.md)
