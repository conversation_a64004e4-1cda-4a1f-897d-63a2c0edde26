# SakuraPanel 故障排除指南

本文档收集了 SakuraPanel 使用过程中的常见问题和解决方案。

## 🚨 常见问题分类

### 安装配置问题
- [数据库连接失败](#数据库连接失败)
- [文件权限错误](#文件权限错误)
- [PHP扩展缺失](#php扩展缺失)
- [Web服务器配置](#web服务器配置)

### 功能使用问题
- [用户无法登录](#用户无法登录)
- [节点状态异常](#节点状态异常)
- [隧道创建失败](#隧道创建失败)
- [流量统计不准确](#流量统计不准确)

### 性能问题
- [页面加载缓慢](#页面加载缓慢)
- [数据库查询慢](#数据库查询慢)
- [内存使用过高](#内存使用过高)

## 🔧 安装配置问题

### 数据库连接失败

**症状**: 
- 页面显示数据库连接错误
- 无法访问任何功能

**可能原因**:
1. 数据库服务未启动
2. 连接参数错误
3. 用户权限不足
4. 防火墙阻止连接

**解决方案**:
```bash
# 1. 检查数据库服务状态
systemctl status mysql
# 或
systemctl status mariadb

# 2. 启动数据库服务
systemctl start mysql

# 3. 测试数据库连接
mysql -h localhost -u your_user -p your_database

# 4. 检查配置文件
cat configuration.php | grep db_
```

**配置检查**:
```php
// configuration.php 正确配置示例
'db_host' => 'localhost',
'db_port' => 3306,
'db_user' => 'sakura_user',
'db_pass' => 'correct_password',
'db_name' => 'sakura_panel',
'db_code' => 'utf8mb4',
```

### 文件权限错误

**症状**:
- 403 Forbidden 错误
- 无法写入文件
- 上传功能失败

**解决方案**:
```bash
# 设置正确的文件权限
chown -R www-data:www-data /var/www/html/
chmod -R 755 /var/www/html/
chmod 644 /var/www/html/configuration.php

# 检查SELinux状态 (CentOS/RHEL)
getenforce
# 如果是Enforcing，可能需要设置SELinux策略
setsebool -P httpd_can_network_connect 1
```

### PHP扩展缺失

**症状**:
- Fatal error: Call to undefined function
- 功能无法正常工作

**检查方法**:
```bash
# 检查PHP扩展
php -m | grep -E "(mysqli|curl|json|mbstring)"

# 检查PHP配置
php -i | grep -E "(mysqli|curl)"
```

**安装扩展**:
```bash
# Ubuntu/Debian
apt-get install php-mysqli php-curl php-json php-mbstring

# CentOS/RHEL
yum install php-mysqli php-curl php-json php-mbstring

# 重启Web服务器
systemctl restart apache2
# 或
systemctl restart nginx
```

## 🔐 功能使用问题

### 用户无法登录

**症状**:
- 用户名密码正确但无法登录
- 登录后立即退出

**排查步骤**:
1. **检查用户状态**:
```sql
SELECT username, status, group FROM users WHERE username = 'your_username';
```

2. **检查Session配置**:
```php
// 检查PHP session配置
session_start();
var_dump($_SESSION);
```

3. **检查密码哈希**:
```php
// 验证密码
$password = 'user_input_password';
$hash = 'database_password_hash';
var_dump(password_verify($password, $hash));
```

**解决方案**:
- 确保用户状态为1（启用）
- 检查session存储路径权限
- 重置用户密码

### 节点状态异常

**症状**:
- 节点显示离线但实际在线
- 节点检测超时
- API连接失败

**排查步骤**:
1. **检查节点配置**:
```sql
SELECT * FROM nodes WHERE id = your_node_id;
```

2. **测试API连接**:
```bash
# 测试管理API
curl -u admin:your_admin_pass http://node_ip:admin_port/api/status
```

3. **检查防火墙**:
```bash
# 检查端口是否开放
telnet node_ip admin_port
```

**解决方案**:
- 确认admin_port和admin_pass配置正确
- 检查FRP服务端管理API是否启用
- 配置防火墙规则允许连接

### 隧道创建失败

**症状**:
- 创建隧道时报错
- 隧道状态异常
- 端口冲突

**常见错误**:
1. **端口被占用**:
```sql
-- 检查端口使用情况
SELECT * FROM proxies WHERE remote_port = your_port AND node = your_node;
```

2. **用户配额不足**:
```sql
-- 检查用户隧道数量
SELECT COUNT(*) FROM proxies WHERE username = 'your_username';
-- 检查用户组限制
SELECT p.proxies FROM groups g JOIN users u ON g.name = u.group WHERE u.username = 'your_username';
```

3. **节点权限不足**:
```sql
-- 检查节点权限
SELECT n.group, u.group FROM nodes n, users u WHERE n.id = your_node_id AND u.username = 'your_username';
```

## 📊 性能问题

### 页面加载缓慢

**排查方法**:
1. **检查数据库查询**:
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;
```

2. **检查PHP性能**:
```php
// 在页面开头添加
$start_time = microtime(true);

// 在页面结尾添加
$end_time = microtime(true);
echo "页面执行时间: " . ($end_time - $start_time) . " 秒";
```

**优化建议**:
- 启用PHP OPcache
- 优化数据库查询
- 使用CDN加速静态资源

### 数据库查询慢

**优化方案**:
```sql
-- 添加必要索引
ALTER TABLE traffic ADD INDEX idx_date_user (date, username);
ALTER TABLE proxies ADD INDEX idx_user_node (username, node);

-- 清理过期数据
DELETE FROM traffic WHERE date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);

-- 优化表结构
OPTIMIZE TABLE traffic;
OPTIMIZE TABLE proxies;
```

## 🔍 调试工具

### 日志文件位置
```bash
# Apache错误日志
tail -f /var/log/apache2/error.log

# Nginx错误日志  
tail -f /var/log/nginx/error.log

# PHP错误日志
tail -f /var/log/php/error.log

# MySQL错误日志
tail -f /var/log/mysql/error.log
```

### 调试代码
```php
// 启用PHP错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 数据库调试
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

// 输出调试信息
var_dump($variable);
error_log("Debug info: " . print_r($data, true));
```

### 测试脚本
使用项目中的 `test_node_status.php` 测试节点功能：
```bash
php test_node_status.php
```

## 📞 获取帮助

### 信息收集
在寻求帮助时，请提供以下信息：

1. **系统环境**:
   - 操作系统版本
   - PHP版本
   - 数据库版本
   - Web服务器版本

2. **错误信息**:
   - 完整的错误消息
   - 错误发生的具体步骤
   - 相关日志内容

3. **配置信息**:
   - 相关配置文件内容（隐藏敏感信息）
   - 数据库表结构
   - 网络环境信息

### 联系方式
- GitHub Issues: 提交详细的问题报告
- 技术交流群: 加入社区讨论
- 邮件支持: 发送详细问题描述

## 🛡️ 预防措施

### 定期维护
1. **备份数据**: 定期备份数据库和配置文件
2. **更新系统**: 及时更新系统和软件包
3. **监控日志**: 定期检查错误日志
4. **性能监控**: 监控系统资源使用情况

### 安全检查
1. **权限审计**: 定期检查文件和数据库权限
2. **密码强度**: 使用强密码并定期更换
3. **访问控制**: 限制管理接口访问
4. **SSL证书**: 使用HTTPS加密传输

---

**相关文档**:
- [安装配置指南](installation.md)
- [API接口文档](api.md)
- [数据库结构](database.md)
