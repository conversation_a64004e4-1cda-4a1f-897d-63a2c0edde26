# SakuraPanel API 接口文档

本文档详细介绍了 SakuraPanel 提供的 API 接口，主要用于与 FRP 服务端进行通信。

## 📡 API 概述

### 基础信息
- **API 地址**: `/api/index.php`
- **认证方式**: Token 认证
- **数据格式**: JSON / 纯文本
- **请求方式**: GET / POST

### 认证机制
API 使用双重认证机制：
1. **服务端认证**: `apitoken` 参数，格式为 `API_TOKEN|NODE_ID`
2. **用户认证**: `token` 参数，用户的16位认证令牌

## 🔐 认证配置

### API Token 配置
```php
// api/index.php
define("API_TOKEN", "your_secure_token_here");
```

### FRP 服务端配置
```ini
# frps.ini
[common]
bind_port = 7000
dashboard_port = 7500
dashboard_user = admin
dashboard_pwd = your_admin_password

# API配置
admin_addr = 127.0.0.1
admin_port = 7400
admin_user = admin
admin_pwd = your_admin_password

# SakuraPanel集成
auth_method = token
auth_token = your_secure_token_here
```

## 📋 API 接口列表

### 1. 状态检查
**接口**: `GET /api/?action=status`

**描述**: 检查 API 服务状态

**请求参数**: 无

**响应示例**:
```json
{
    "status": 200,
    "message": "SakuraPanel API is running",
    "version": "1.0.2",
    "timestamp": "2025-07-28 10:30:00",
    "available_actions": [
        "status",
        "getconf",
        "checktoken", 
        "checkproxy",
        "getlimit"
    ]
}
```

### 2. 获取用户配置
**接口**: `GET /api/?action=getconf`

**描述**: 获取用户的 FRP 配置文件

**请求参数**:
- `token` (string): 用户认证令牌
- `node` (int): 节点ID

**请求示例**:
```
GET /api/?action=getconf&token=abcd1234efgh5678&node=1
```

**响应**: FRP 配置文件内容 (纯文本)
```ini
[common]
server_addr = *************
server_port = 7000
token = user_token_here

[web_proxy_80]
type = http
local_ip = 127.0.0.1
local_port = 80
custom_domains = example.com
```

### 3. 验证用户令牌
**接口**: `GET /api/?action=checktoken`

**描述**: 验证用户令牌的有效性

**请求参数**:
- `user` (string): 用户令牌 (16位)
- `apitoken` (string): API令牌，格式: `API_TOKEN|NODE_ID`

**请求示例**:
```
GET /api/?action=checktoken&user=abcd1234efgh5678&apitoken=your_token|1
```

**响应示例**:
```json
{
    "status": 200,
    "message": "Login successful, welcome!"
}
```

**错误响应**:
```json
{
    "status": 403,
    "message": "Login failed"
}
```

### 4. 验证隧道配置
**接口**: `GET /api/?action=checkproxy`

**描述**: 验证隧道配置的合法性

**请求参数**:
- `user` (string): 用户令牌
- `proxy_name` (string): 隧道名称
- `proxy_type` (string): 隧道类型 (tcp/udp/http/https/stcp/xtcp)
- `apitoken` (string): API令牌

**TCP/UDP 隧道额外参数**:
- `remote_port` (int): 远程端口

**HTTP/HTTPS 隧道额外参数**:
- `domain` (string): 自定义域名
- `subdomain` (string): 子域名

**STCP/XTCP 隧道额外参数**:
- `sk` (string): 密钥

**请求示例**:
```
# TCP隧道
GET /api/?action=checkproxy&user=abcd1234efgh5678&proxy_name=ssh&proxy_type=tcp&remote_port=2222&apitoken=your_token|1

# HTTP隧道  
GET /api/?action=checkproxy&user=abcd1234efgh5678&proxy_name=web&proxy_type=http&domain=example.com&apitoken=your_token|1
```

**响应示例**:
```json
{
    "status": 200,
    "message": "Proxy exist"
}
```

### 5. 获取流量限制
**接口**: `GET /api/?action=getlimit`

**描述**: 获取用户的流量限制信息

**请求参数**:
- `user` (string): 用户令牌
- `apitoken` (string): API令牌

**请求示例**:
```
GET /api/?action=getlimit&user=abcd1234efgh5678&apitoken=your_token|1
```

**响应示例**:
```json
{
    "status": 200,
    "max-in": 1024.0,
    "max-out": 1024.0
}
```

## 🔧 内部管理 API

### 节点状态检测
**接口**: `POST /?action=checknodestatus`

**描述**: 检测单个节点状态 (管理员功能)

**请求参数**:
- `id` (int): 节点ID
- `csrf` (string): CSRF令牌

**响应示例**:
```json
{
    "status": "online",
    "code": 200,
    "message": "节点在线",
    "response_time": 45,
    "server_info": {
        "version": "0.44.0",
        "status": "running"
    }
}
```

### 批量节点检测
**接口**: `POST /?action=checkallnodesstatus`

**描述**: 批量检测所有节点状态 (管理员功能)

**请求参数**:
- `csrf` (string): CSRF令牌

**响应示例**:
```json
{
    "1": {
        "status": "online",
        "code": 200,
        "message": "节点在线",
        "response_time": 45
    },
    "2": {
        "status": "offline", 
        "code": 500,
        "message": "节点离线或连接失败",
        "response_time": 5000
    }
}
```

## 📊 状态码说明

### HTTP 状态码
- `200` - 请求成功
- `400` - 请求参数错误
- `401` - 认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

### 节点状态码
- `200` - 正常在线
- `401` - 隐藏状态
- `403` - 禁用状态
- `500` - 离线状态
- `404` - 节点不存在

## 🛡️ 安全考虑

### 1. Token 安全
- API Token 应该使用强随机字符串
- 定期更换 API Token
- 不要在日志中记录 Token

### 2. 网络安全
- 建议使用 HTTPS
- 限制 API 访问来源
- 设置合理的超时时间

### 3. 权限控制
- 严格验证用户权限
- 记录 API 访问日志
- 监控异常访问

## 🧪 测试示例

### 使用 curl 测试
```bash
# 检查API状态
curl "http://your-domain.com/api/?action=status"

# 验证用户令牌
curl "http://your-domain.com/api/?action=checktoken&user=abcd1234efgh5678&apitoken=your_token|1"

# 获取用户配置
curl "http://your-domain.com/api/?action=getconf&token=abcd1234efgh5678&node=1"
```

### 使用 PHP 测试
```php
<?php
$api_base = "http://your-domain.com/api/";
$token = "abcd1234efgh5678";
$node_id = 1;

// 获取配置
$url = $api_base . "?action=getconf&token=" . $token . "&node=" . $node_id;
$config = file_get_contents($url);
echo $config;
?>
```

## 📝 集成指南

### FRP 服务端集成
1. 配置 FRP 服务端的认证插件
2. 设置正确的 API Token
3. 配置管理端口和密码
4. 测试 API 连接

### 客户端集成
1. 从 API 获取配置文件
2. 验证令牌有效性
3. 定期检查配置更新
4. 处理错误和重试

---

**相关文档**: 
- [节点状态检测功能说明](节点状态检测功能说明.md)
- [安装配置指南](installation.md)
