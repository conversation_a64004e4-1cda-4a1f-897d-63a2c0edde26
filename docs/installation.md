# SakuraPanel 安装配置指南

本文档详细介绍了 SakuraPanel 的安装和配置过程。

## 📋 环境要求

### 基础环境
- **操作系统**: Linux (推荐 Ubuntu 18.04+, CentOS 7+)
- **Web服务器**: Apache 2.4+ 或 Nginx 1.14+
- **PHP版本**: PHP 7.0+ (推荐 PHP 7.4+)
- **数据库**: MySQL 5.7+ 或 MariaDB 10.2+

### PHP扩展要求
```bash
# 必需扩展
php-mysqli
php-curl
php-json
php-mbstring
php-session

# 推荐扩展
php-opcache
php-zip
php-gd
```

### 系统权限
- Web目录读写权限
- PHP执行权限
- 数据库连接权限

## 🚀 安装步骤

### 1. 下载源码
```bash
# 使用 Git 克隆
git clone https://github.com/ZeroDream-CN/SakuraPanel.git
cd SakuraPanel

# 或下载压缩包
wget https://github.com/ZeroDream-CN/SakuraPanel/archive/main.zip
unzip main.zip
```

### 2. 部署文件
```bash
# 复制到Web目录
cp -r SakuraPanel/* /var/www/html/

# 设置权限
chown -R www-data:www-data /var/www/html/
chmod -R 755 /var/www/html/
```

### 3. 创建数据库
```sql
-- 创建数据库
CREATE DATABASE sakura_panel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'sakura_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON sakura_panel.* TO 'sakura_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. 导入数据库结构
```bash
mysql -u sakura_user -p sakura_panel < import.sql
```

### 5. 配置系统

#### 修改 configuration.php
```php
<?php
$_config = Array(
    // 站点信息
    'sitename'      => '您的站点名称',
    'description'   => '内网穿透管理面板',
    
    // 数据库配置
    'db_host'       => 'localhost',
    'db_port'       => 3306,
    'db_user'       => 'sakura_user',
    'db_pass'       => 'your_password',
    'db_name'       => 'sakura_panel',
    'db_code'       => 'utf8mb4',
    
    // 注册设置
    'register' => Array(
        'enable'    => true,        // 是否允许注册
        'traffic'   => 1024,        // 新用户流量(MB)
        'proxies'   => 3,           // 新用户隧道数量
        'invite'    => false        // 是否需要邀请码
    ),
    
    // 邮件配置
    'smtp' => Array(
        'enable'    => true,
        'host'      => 'smtp.example.com',
        'port'      => 587,
        'user'      => '<EMAIL>',
        'pass'      => 'email_password',
        'mail'      => '<EMAIL>'
    ),
    
    // 其他配置...
);
?>
```

#### 配置 API Token (api/index.php)
```php
// 修改API密码，需要和 Frps.ini 里面设置的一样
define("API_TOKEN", "your_secure_token_here");
```

#### 配置守护进程 (daemon.php)
```php
$db = [
    "host" => "localhost",
    "user" => "sakura_user", 
    "pass" => "your_password",
    "name" => "sakura_panel",
    "port" => 3306
];
```

### 6. Web服务器配置

#### Apache 配置
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        AllowOverride All
        Require all granted
    </Directory>
    
    # 可选：重定向到HTTPS
    # Redirect permanent / https://your-domain.com/
</VirtualHost>
```

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.php index.html;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
}
```

## 🔧 初始化配置

### 1. 创建管理员账号
```bash
# 访问网站注册第一个账号
# 然后在数据库中设置为管理员
mysql -u sakura_user -p sakura_panel

UPDATE users SET `group` = 'admin' WHERE username = 'your_admin_username';
```

### 2. 配置用户组
```sql
-- 查看默认用户组
SELECT * FROM groups;

-- 可以根据需要修改用户组配置
UPDATE groups SET traffic = 2048 WHERE name = 'default';
```

### 3. 添加服务器节点
登录管理员账号后：
1. 进入"服务器节点"页面
2. 点击右侧添加节点
3. 填写节点信息：
   - 节点名称
   - IP地址
   - FRP端口
   - 管理端口和密码
   - 允许的用户组

## 🔒 安全配置

### 1. 文件权限
```bash
# 设置合适的文件权限
find /var/www/html -type f -exec chmod 644 {} \;
find /var/www/html -type d -exec chmod 755 {} \;

# 保护敏感文件
chmod 600 /var/www/html/configuration.php
```

### 2. 数据库安全
```sql
-- 删除测试数据库
DROP DATABASE IF EXISTS test;

-- 删除匿名用户
DELETE FROM mysql.user WHERE User='';

-- 禁止root远程登录
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');

FLUSH PRIVILEGES;
```

### 3. Web服务器安全
```apache
# Apache .htaccess 示例
<Files "configuration.php">
    Order allow,deny
    Deny from all
</Files>

<Files "daemon.php">
    Order allow,deny
    Deny from all
</Files>
```

## 🧪 测试安装

### 1. 基础功能测试
- [ ] 访问首页是否正常
- [ ] 用户注册是否正常
- [ ] 用户登录是否正常
- [ ] 管理员权限是否正确

### 2. 节点功能测试
- [ ] 添加节点是否成功
- [ ] 节点状态检测是否正常
- [ ] API接口是否响应

### 3. 隧道功能测试
- [ ] 创建隧道是否成功
- [ ] 隧道配置是否正确
- [ ] 流量统计是否正常

## 🚨 常见问题

### 数据库连接失败
```
检查项目：
1. 数据库服务是否启动
2. 用户名密码是否正确
3. 数据库名称是否存在
4. PHP mysqli扩展是否安装
```

### 权限错误
```
解决方法：
1. 检查文件权限设置
2. 确认Web服务器用户
3. 检查SELinux设置
```

### 邮件发送失败
```
检查项目：
1. SMTP配置是否正确
2. 邮箱密码是否正确
3. 防火墙是否阻止SMTP端口
4. 邮件服务商是否需要特殊设置
```

## 📞 获取帮助

如果安装过程中遇到问题：

1. 查看错误日志
2. 检查配置文件
3. 参考故障排除文档
4. 提交Issue寻求帮助

---

**下一步**: 阅读 [用户管理系统](user-management.md) 了解如何管理用户。
