<?php
namespace SakuraPanel;

use SakuraPanel;

class NodeManager {
	
	public function closeClient($node, $token)
	{
		$ninfo = $this->getNodeInfo($node);
		if($ninfo) {
			$result = SakuraPanel\Utils::http("http://admin:{$ninfo['admin_pass']}@{$ninfo['ip']}:{$ninfo['admin_port']}/api/client/close/{$token}");
			if(isset($result['body'])) {
				$json   = json_decode($result['body'], true);
				if(is_array($json)) {
					if($json['status'] == 200) {
						return true;
					} else {
						return $json['message'];
					}
				} else {
					return false;
				}
			} else {
				return false;
			}
		} else {
			return false;
		}
	}
	
	public function getUserNode($group)
	{
		return Database::toArray(Database::search("nodes", Array("group" => "{$group};")));
	}
	
	public function isNodeExist($node)
	{
		return Database::querySingleLine("nodes", Array("id" => $node)) ? true : false;
	}
	
	public function getNodeInfo($node)
	{
		return Database::querySingleLine("nodes", Array("id" => $node));
	}
	
	public function updateNode($id, $data)
	{
		if($this->getNodeInfo($id)) {
			return Database::update("nodes", $data, Array("id" => $id));
		} else {
			return false;
		}
	}
	
	public function getTotalNodes()
	{
		$rs = Database::toArray(Database::query("nodes", Array()));
		return count($rs);
	}
	
	public function isUserHasPermission($user, $node)
	{
		$um = new SakuraPanel\UserManager();
		$ns = $this->getNodeInfo($node);
		$us = $um->getInfoByUser($user);
		if(is_array($us) && is_array($ns)) {
			if(stristr($ns['group'], "{$us['group']};")) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}
	
	public function isNodeAvailable($node)
	{
		$ns = $this->getNodeInfo($node);
		return Intval($ns['status']) == 200;
	}
	
	public function addNode($data)
	{
		return Database::insert("nodes", $data);
	}
	
	public function deleteNode($data)
	{
		$result = false;

		if(is_array($this->getNodeInfo($data))) {
			$result = Database::delete("proxies", Array("node" => $data));

			if($result !== true) {
				return $result;
			}

			return Database::delete("nodes", Array("id" => $data));
		} else {
			return false;
		}
	}

	/**
	 * 检测节点连接状态
	 * @param int $nodeId 节点ID
	 * @return array 返回检测结果
	 */
	public function checkNodeStatus($nodeId)
	{
		$nodeInfo = $this->getNodeInfo($nodeId);
		if (!$nodeInfo) {
			return [
				'status' => 'error',
				'code' => 404,
				'message' => '节点不存在',
				'response_time' => 0
			];
		}

		// 如果节点被手动设置为隐藏或禁用，直接返回对应状态
		$manualStatus = intval($nodeInfo['status']);
		if ($manualStatus === 401) {
			return [
				'status' => 'hidden',
				'code' => 401,
				'message' => '节点已隐藏',
				'response_time' => 0
			];
		}
		if ($manualStatus === 403) {
			return [
				'status' => 'disabled',
				'code' => 403,
				'message' => '节点已禁用',
				'response_time' => 0
			];
		}

		// 检查必要的连接信息
		if (empty($nodeInfo['admin_port']) || empty($nodeInfo['admin_pass'])) {
			return [
				'status' => 'offline',
				'code' => 500,
				'message' => '节点配置不完整（缺少管理端口或密码）',
				'response_time' => 0
			];
		}

		$startTime = microtime(true);

		// 尝试连接节点的管理API
		$url = "http://admin:{$nodeInfo['admin_pass']}@{$nodeInfo['ip']}:{$nodeInfo['admin_port']}/api/status";
		$result = SakuraPanel\Utils::http($url, '', '', '', 0, 5); // 5秒超时

		$responseTime = round((microtime(true) - $startTime) * 1000); // 毫秒

		// 检查HTTP请求是否成功
		if (isset($result['status']) && $result['status'] == 200 && isset($result['body'])) {
			$json = json_decode($result['body'], true);
			if (is_array($json)) {
				// 连接成功，更新节点状态为正常
				if ($manualStatus !== 200) {
					$this->updateNodeStatus($nodeId, 200);
				}
				return [
					'status' => 'online',
					'code' => 200,
					'message' => '节点在线',
					'response_time' => $responseTime,
					'server_info' => $json
				];
			}
		}

		// 连接失败，自动设置为离线状态
		if ($manualStatus !== 500) {
			$this->updateNodeStatus($nodeId, 500);
		}

		// 提供更详细的错误信息
		$errorMessage = '节点离线或连接失败';
		if (isset($result['status']) && is_string($result['status'])) {
			$errorMessage .= ' (错误: ' . $result['status'] . ')';
		} elseif (isset($result['status'])) {
			$errorMessage .= ' (HTTP状态码: ' . $result['status'] . ')';
		}

		return [
			'status' => 'offline',
			'code' => 500,
			'message' => $errorMessage,
			'response_time' => $responseTime
		];
	}

	/**
	 * 更新节点状态
	 * @param int $nodeId 节点ID
	 * @param int $status 状态码
	 * @return bool
	 */
	public function updateNodeStatus($nodeId, $status)
	{
		return Database::update("nodes", ['status' => $status], ['id' => $nodeId]);
	}

	/**
	 * 批量检测所有节点状态
	 * @return array 所有节点的状态信息
	 */
	public function checkAllNodesStatus()
	{
		// 修复：使用正确的方法获取关联数组
		$result = Database::query("nodes", []);
		$nodes = [];
		if ($result) {
			while ($row = mysqli_fetch_assoc($result)) {
				$nodes[] = $row;
			}
		}
		$results = [];

		foreach ($nodes as $node) {
			$results[$node['id']] = $this->checkNodeStatus($node['id']);
		}

		return $results;
	}

	/**
	 * 获取节点状态文本
	 * @param int $statusCode 状态码
	 * @return string 状态文本
	 */
	public function getStatusText($statusCode)
	{
		$statusMap = [
			200 => '正常',
			401 => '隐藏',
			403 => '禁用',
			500 => '离线',
			404 => '异常'
		];

		return $statusMap[$statusCode] ?? '未知';
	}

	/**
	 * 获取节点状态颜色类
	 * @param int $statusCode 状态码
	 * @return string CSS类名
	 */
	public function getStatusClass($statusCode)
	{
		$classMap = [
			200 => 'text-success',
			401 => 'text-secondary',
			403 => 'text-warning',
			500 => 'text-danger',
			404 => 'text-danger'
		];

		return $classMap[$statusCode] ?? 'text-muted';
	}
}
