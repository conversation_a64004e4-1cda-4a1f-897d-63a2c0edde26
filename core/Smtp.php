<?php
namespace SakuraPanel;

class Smtp {
	
    public $smtp_port;
    public $time_out;
    public $host_name;
    public $log_file;
    public $relay_host;
    public $debug;
    public $auth;
    public $user;
    public $pass;
	
    private $sock;
	
    public function __construct($relay_host = "", $smtp_port = 25, $auth = false, $user, $pass)
	{
        $this->debug      = false;
        $this->smtp_port  = $smtp_port;
        $this->relay_host = $relay_host;
        $this->time_out   = 30;
        $this->auth       = $auth;
        $this->user       = $user;
        $this->pass       = $pass;
        $this->host_name  = "localhost";
        $this->log_file   = "";
        $this->sock       = false;
    }
	
    public function sendMail($to, $from, $subject = "", $body = "", $mailtype, $cc = "", $bcc = "", $additional_headers = "")
	{
        // 引入PHPMailer
        require_once(ROOT . '/core/PHPMailer/PHPMailer.php');
        require_once(ROOT . '/core/PHPMailer/SMTP.php');
        require_once(ROOT . '/core/PHPMailer/Exception.php');
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);

        try {
            $mail->isSMTP();
            // 兼容原有配置
            $mail->Host = str_replace('ssl://', '', $this->relay_host);
            $mail->SMTPAuth = $this->auth;
            $mail->Username = $this->user;
            $mail->Password = $this->pass;
            $mail->CharSet = 'UTF-8';
            // 判断是否SSL
            if (strpos($this->relay_host, 'ssl://') === 0) {
                $mail->SMTPSecure = 'ssl';
        }
            $mail->Port = $this->smtp_port;

            $mail->setFrom($from);
            foreach (explode(',', $to) as $addr) {
                $addr = trim($addr);
                if ($addr) $mail->addAddress($addr);
            }
            if ($cc) {
                foreach (explode(',', $cc) as $addr) {
                    $addr = trim($addr);
                    if ($addr) $mail->addCC($addr);
                }
        }
            if ($bcc) {
                foreach (explode(',', $bcc) as $addr) {
                    $addr = trim($addr);
                    if ($addr) $mail->addBCC($addr);
                }
            }
            $mail->isHTML(strtoupper($mailtype) === 'HTML');
            $mail->Subject = $subject;
            $mail->Body = $body;

            $mail->send();
            return true;
        } catch (Exception $e) {
            if ($this->debug) echo "Mailer Error: " . $mail->ErrorInfo;
            return false;
        }
    }
	
    private function smtpSend($helo, $from, $to, $header, $body = "")
	{
        if (!$this->smtpPutcmd("HELO", $helo)) {
            return $this->smtpError("sending HELO command");
        }
        if ($this->auth) {
            if (!$this->smtpPutcmd("AUTH LOGIN", base64_encode($this->user))) {
                return $this->smtpError("sending HELO command");
            }
            if (!$this->smtpPutcmd("", base64_encode($this->pass))) {
                return $this->smtpError("sending HELO command");
            }
        }
        if (!$this->smtpPutcmd("MAIL", "FROM:<{$from}>")) {
            return $this->smtpError("sending MAIL FROM command");
        }
        if (!$this->smtpPutcmd("RCPT", "TO:<{$to}>")) {
            return $this->smtpError("sending RCPT TO command");
        }
        if (!$this->smtpPutcmd("DATA")) {
            return $this->smtpError("sending DATA command");
        }
        if (!$this->smtpMessage($header, $body)) {
            return $this->smtpError("sending message");
        }
        if (!$this->smtpEom()) {
            return $this->smtpError("sending <CR><LF>.<CR><LF> [EOM]");
        }
        if (!$this->smtpPutcmd("QUIT")) {
            return $this->smtpError("sending QUIT command");
        }
        return true;
    }
	
    private function smtpSockopen($address)
	{
        if ($this->relay_host == "") {
            return $this->smtpSockopenMx($address);
        } else {
            return $this->smtpSockopenRelay();
        }
    }
	
    private function smtpSockopenRelay()
	{
        $this->logWrite("Trying to {$this->relay_host}:{$this->smtp_port}\n");
        $this->sock = @fsockopen($this->relay_host, $this->smtp_port, $errno, $errstr, $this->time_out);
        if (!($this->sock && $this->smtpOk())) {
            $this->logWrite("Error: Cannot connenct to relay host {$this->relay_host}\n");
            $this->logWrite("Error: {$errstr} ({$errno})\n");
            return false;
        }
        $this->logWrite("Connected to relay host {$this->relay_host}\n");
        return true;;
    }
	
    private function smtpSockopenMx($address)
	{
        $domain = ereg_replace("^.+@([^@]+)$", "\\1", $address);
        if (!@getmxrr($domain, $MXHOSTS)) {
            $this->logWrite("Error: Cannot resolve MX \"{$domain}\"\n");
            return false;
        }
        foreach ($MXHOSTS as $host) {
            $this->logWrite("Trying to {$host}:{$this->smtp_port}\n");
            $this->sock = @fsockopen($host, $this->smtp_port, $errno, $errstr, $this->time_out);
            if (!($this->sock && $this->smtpOk())) {
                $this->logWrite("Warning: Cannot connect to mx host {$host}\n");
                $this->logWrite("Error: {$errstr} ({$errno})\n");
                continue;
            }
            $this->logWrite("Connected to mx host {$host}\n");
            return true;
        }
        $this->logWrite("Error: Cannot connect to any mx hosts (" . implode(", ", $MXHOSTS) . ")\n");
        return false;
    }
	
    private function smtpMessage($header, $body)
	{
        // 统一换行符为 \r\n
        $header = preg_replace("/(\r\n|\r|\n)/", "\r\n", $header);
        $body = preg_replace("/(\r\n|\r|\n)/", "\r\n", $body);

        // 头部字段进一步规范化
        // Subject中文MIME编码
        if (preg_match('/^Subject: (.*)$/mi', $header, $matches)) {
            $subject = $matches[1];
            if (!preg_match('/^=\?UTF-8\?B\?.*\?=$/', $subject)) {
                $encoded = 'Subject: =?UTF-8?B?' . base64_encode($subject) . '?=';
                $header = preg_replace('/^Subject: .*$/mi', $encoded, $header);
            }
        }
        // From/To用尖括号
        $header = preg_replace('/^From: ?([^<].*[^>])$/mi', 'From: <$1>', $header);
        $header = preg_replace('/^To: ?([^<].*[^>])$/mi', 'To: <$1>', $header);

        // header和body之间必须有且只有一个空行
        $msg = $header . "\r\n\r\n" . $body;

        fputs($this->sock, $msg);
        $this->smtpDebug("> " . str_replace("\r\n", "\n> ", $msg . "\n> "));
        return true;
    }
	
    private function smtpEom()
	{
        fputs($this->sock, "\r\n.\r\n");
        $this->smtpDebug(". [EOM]\n");
        return $this->smtpOk();
    }
	
    private function smtpOk()
	{
        $response = str_replace("\r\n", "", fgets($this->sock, 512));
        $this->smtpDebug("{$response}\n");
        if (!mb_ereg("^[23]", $response)) {
            fputs($this->sock, "QUIT\r\n");
            fgets($this->sock, 512);
            $this->logWrite("Error: Remote host returned \"{$response}\"\n");
            return false;
        }
        return true;
    }
	
    private function smtpPutcmd($cmd, $arg = "")
	{
        if ($arg != "") {
			$cmd = empty($cmd) ? $arg : "{$cmd} {$arg}";
        }
        fputs($this->sock, "{$cmd}\r\n");
        $this->smtpDebug("> {$cmd}\n");
        return $this->smtpOk();
    }
	
    private function smtpError($string)
	{
        $this->logWrite("Error: Error occurred while {$string}.\n");
        return false;
    }
	
    private function logWrite($message)
	{
        $this->smtpDebug($message);
        if ($this->log_file == "") {
            return true;
        }
        $message = date("M d H:i:s ") . get_current_user() . "[" . getmypid() . "]: {$message}";
        if (!@file_exists($this->log_file) || !($fp = @fopen($this->log_file, "a"))) {
            $this->smtpDebug("Warning: Cannot open log file \"{$this->log_file}\"\n");
            return false;
        }
        flock($fp, LOCK_EX);
        fputs($fp, $message);
        fclose($fp);
        return true;
    }
	
    private function stripComment($address)
	{
        $comment = "\\([^()]*\\)";
        while (mb_ereg($comment, $address)) {
            $address = mb_ereg_replace($comment, "", $address);
        }
        return $address;
    }
	
    private function getAddress($address)
	{
        $address = mb_ereg_replace("([ \t\r\n])+", "", $address);
        $address = mb_ereg_replace("^.*<(.+)>.*$", "\\1", $address);
        return $address;
    }
	
    private function smtpDebug($message)
	{
        if ($this->debug) {
            echo $message;
        }
    }
	
    private function getAttachType($image_tag)
    {
        $filedata     = array();
        $img_file_con = fopen($image_tag, "r");
        unset($image_data);
        
		while ($tem_buffer = addslashes(fread($img_file_con, filesize($image_tag)))) {
			$image_data .= $tem_buffer;
		}
        fclose($img_file_con);
        
		$filedata['context']  = $image_data;
        $filedata['filename'] = basename($image_tag);
        $extension            = substr($image_tag, strrpos($image_tag, "."), strlen($image_tag) - strrpos($image_tag, "."));
		
		$extensions = Array(
			".gif"  => "image/gif",
			".gz"   => "application/x-gzip",
			".htm"  => "text/html",
			".html" => "text/html",
			".jpg"  => "image/jpeg",
			".png"  => "image/png",
			".bmp"  => "image/bmp",
			".gif"  => "image/gif",
			".tar"  => "application/x-tar",
			".txt"  => "text/plain",
			".zip"  => "application/zip"
		);
		
		$filedata['type'] = $extensions[$extension] ?? "application/octet-stream";
        
		return $filedata;
    }
}
