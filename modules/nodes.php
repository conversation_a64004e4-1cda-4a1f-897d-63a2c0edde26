<?php
namespace SakuraPanel;

use SakuraPanel;

$page_title = "服务器节点";
$nm = new SakuraPanel\NodeManager();
$rs = Database::querySingleLine("users", Array("username" => $_SESSION['user']));

if(!$rs || $rs['group'] !== "admin") {
	exit("<script>location='?page=panel';</script>");
}

if(isset($_GET['getinfo']) && preg_match("/^[0-9]{1,10}$/", $_GET['getinfo'])) {
	SakuraPanel\Utils::checkCsrf();
	$rs = Database::querySingleLine("nodes", Array("id" => $_GET['getinfo']));
	if($rs) {
		ob_clean();
		exit(json_encode($rs));
	} else {
		ob_clean();
		Header("HTTP/1.1 403");
		exit("未找到用户");
	}
}
?>
<style type="text/css">
/* 节点管理页面样式 */
.content {
    padding: 0 1rem;
}

.node-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    margin-bottom: 1.25rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.node-card:hover {
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.node-status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
}

.status-online { background-color: #d4edda; color: #155724; }
.status-offline { background-color: #f8d7da; color: #721c24; }
.status-disabled { background-color: #fff3cd; color: #856404; }
.status-hidden { background-color: #e2e3e5; color: #383d41; }

.response-time {
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 50px;
    text-align: center;
}

.response-good { color: #28a745; }
.response-medium { color: #ffc107; }
.response-poor { color: #dc3545; }

.node-actions {
    display: flex;
    gap: 0.375rem;
    justify-content: center;
    flex-wrap: wrap;
}

.node-actions .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    min-width: 36px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.stats-card .card-body {
    padding: 1.25rem;
}

.stats-number {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stats-label {
    font-size: 0.8rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.form-section {
    background-color: #f8f9fc;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1.25rem;
    border: 1px solid #e3e6f0;
}

.form-section-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e3e6f0;
}

.compact-table {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.compact-table th {
    background-color: #f8f9fc;
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
    border-bottom: 2px solid #e3e6f0;
}

.compact-table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #e3e6f0;
}

.compact-table tbody tr:hover {
    background-color: #f8f9fc;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 1.5rem;
    margin: 0 0 1.5rem 0;
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.5rem;
}

/* 卡片间距调整 */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e3e6f0;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

/* 表单样式微调 */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    border-radius: 0.375rem;
    border: 1px solid #d1d3e2;
    padding: 0.625rem 0.75rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 按钮样式微调 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .content {
        padding: 0 0.75rem;
    }

    .page-header {
        margin: 0 0 1rem 0;
        padding: 1.25rem 1rem;
    }

    .stats-number {
        font-size: 1.5rem;
    }

    .node-actions {
        gap: 0.25rem;
    }

    .node-actions .btn {
        padding: 0.25rem 0.5rem;
        min-width: 32px;
    }
}
</style>
<div class="content-header">
    <div class="container-fluid">
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="fas fa-server mr-2"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-1 opacity-75">管理和监控服务器节点状态</p>
                </div>
                <div class="col-md-6 text-md-right">
                    <div class="d-flex justify-content-md-end align-items-center">
                        <small class="mr-3" id="last-check-time">最后检测: 从未</small>
                        <button type="button" class="btn btn-light btn-sm" onclick="checkAllNodesStatus()" id="check-all-btn">
                            <i class="fas fa-sync-alt mr-1"></i> 检测所有节点
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="content">
    <div class="container-fluid">
        <!-- 统计卡片 -->
        <div class="row mb-3">
            <?php
            $totalNodes = Database::toArray(Database::query("nodes", []));
            $onlineNodes = 0;
            $offlineNodes = 0;
            $disabledNodes = 0;

            foreach($totalNodes as $node) {
                $status = intval($node['status']);
                if($status == 200) $onlineNodes++;
                elseif($status == 500) $offlineNodes++;
                elseif($status == 403) $disabledNodes++;
            }
            ?>
            <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo count($totalNodes); ?></div>
                        <div class="stats-label">总节点数</div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
                <div class="card bg-success text-white" style="border-radius: 0.5rem;">
                    <div class="card-body text-center" style="padding: 1.25rem;">
                        <div class="stats-number"><?php echo $onlineNodes; ?></div>
                        <div class="stats-label">在线节点</div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
                <div class="card bg-danger text-white" style="border-radius: 0.5rem;">
                    <div class="card-body text-center" style="padding: 1.25rem;">
                        <div class="stats-number"><?php echo $offlineNodes; ?></div>
                        <div class="stats-label">离线节点</div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 mb-3">
                <div class="card bg-warning text-white" style="border-radius: 0.5rem;">
                    <div class="card-body text-center" style="padding: 1.25rem;">
                        <div class="stats-number"><?php echo $disabledNodes; ?></div>
                        <div class="stats-label">禁用节点</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xl-8 col-lg-7">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list mr-2"></i>节点列表
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-toggle="collapse" data-target="#status-help">
                                <i class="fas fa-question-circle"></i>
                            </button>
                        </div>
                    </div>
                    <div class="collapse" id="status-help">
                        <div class="card-body border-bottom">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info-circle text-info mr-1"></i>状态说明</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><span class="badge status-online">正常</span> 节点在线且可用</li>
                                        <li><span class="badge status-offline">离线</span> 节点无法连接</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-cog text-secondary mr-1"></i>管理状态</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><span class="badge status-disabled">禁用</span> 管理员手动禁用</li>
                                        <li><span class="badge status-hidden">隐藏</span> 对用户隐藏</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
						<table class="table compact-table mb-0">
							<thead>
								<tr>
									<th style="width: 50px;" class="text-center">ID</th>
									<th style="width: auto;">节点信息</th>
									<th style="width: 100px;" class="text-center">状态</th>
									<th style="width: 90px;" class="text-center">响应时间</th>
									<th style="width: 180px;" class="text-center">操作</th>
								</tr>
							</thead>
							<tbody>
							<?php
							$rs = Database::toArray(Database::query("users", "SELECT * FROM `nodes`", true));
							$i = 0;
							foreach($rs as $node) {
								$i++;
								$statusCode = intval($node[10]);
								$statusText = $nm->getStatusText($statusCode);

								// 状态样式映射
								$statusBadgeClass = '';
								switch($statusCode) {
									case 200: $statusBadgeClass = 'status-online'; break;
									case 401: $statusBadgeClass = 'status-hidden'; break;
									case 403: $statusBadgeClass = 'status-disabled'; break;
									case 500: $statusBadgeClass = 'status-offline'; break;
									default: $statusBadgeClass = 'status-offline'; break;
								}

								echo "<tr id='node-row-{$node[0]}' class='node-row'>
								<td class='text-center font-weight-bold text-primary'>{$node[0]}</td>
								<td>
									<div class='d-flex flex-column'>
										<span class='font-weight-bold text-dark mb-1'>{$node[1]}</span>
										<small class='text-muted mb-1'>
											<i class='fas fa-globe mr-1'></i>{$node[3]}:{$node[5]}
										</small>
										" . (!empty($node[2]) ? "<small class='text-muted'><i class='fas fa-info-circle mr-1'></i>{$node[2]}</small>" : "") . "
									</div>
								</td>
								<td class='text-center'>
									<span class='badge node-status-badge {$statusBadgeClass}' id='status-{$node[0]}'>{$statusText}</span>
								</td>
								<td class='text-center'>
									<span class='response-time' id='response-time-{$node[0]}'>-</span>
								</td>
								<td class='text-center'>
									<div class='node-actions'>
										<button class='btn btn-outline-info btn-sm' onclick='checkNodeStatus({$node[0]})' title='检测状态'>
											<i class='fas fa-search'></i>
										</button>
										<button class='btn btn-outline-primary btn-sm' onclick='edit({$node[0]})' title='编辑节点'>
											<i class='fas fa-edit'></i>
										</button>
										<button class='btn btn-outline-danger btn-sm' onclick='deletenode({$node[0]})' title='删除节点'>
											<i class='fas fa-trash'></i>
										</button>
									</div>
								</td>
								</tr>";
							}
							?>
						</table>
							</tbody>
						</table>
						<?php
						if($i == 0) {
							echo "<div class='text-center py-4'>
								<i class='fas fa-server fa-3x text-muted mb-3'></i>
								<h5 class='text-muted'>暂无节点</h5>
								<p class='text-muted'>请在右侧添加第一个服务器节点</p>
							</div>";
						}
						?>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xl-4 col-lg-5">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus-circle mr-2"></i>
                            <span id="form-title">添加节点</span>
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" onclick="resetForm()" title="重置表单">
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info" id="statusmsg">
                            <i class="fas fa-info-circle mr-2"></i>
                            填写下方表单来添加新的服务器节点
                        </div>

                        <!-- 基本信息 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <i class="fas fa-info-circle mr-1"></i>基本信息
                            </div>
                            <div class="form-group mb-3">
                                <label for="node_name" class="font-weight-bold">节点名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="node_name" placeholder="例如: 香港节点">
                                <small class="form-text text-muted">会显示在隧道列表和创建隧道界面</small>
                            </div>
                            <div class="form-group mb-0">
                                <label for="node_description" class="font-weight-bold">节点简介</label>
                                <input type="text" class="form-control" id="node_description" placeholder="例如: 香港高速节点，适合亚洲用户">
                                <small class="form-text text-muted">用一句简单的话来介绍这个节点</small>
                            </div>
                        </div>

                        <!-- 连接配置 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <i class="fas fa-network-wired mr-1"></i>连接配置
                            </div>
                            <div class="form-group mb-3">
                                <label for="node_hostname" class="font-weight-bold">主机名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="node_hostname" placeholder="例如: hk.example.com">
                                <small class="form-text text-muted">这里可以是一个域名或者 IP 地址</small>
                            </div>
                            <div class="form-group mb-3">
                                <label for="node_ip" class="font-weight-bold">IP 地址 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="node_ip" placeholder="例如: *************">
                                <small class="form-text text-muted">服务器的 IP 地址，请不要输入域名</small>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group mb-3">
                                        <label for="node_port" class="font-weight-bold">FRP 端口 <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="node_port" placeholder="7000">
                                        <small class="form-text text-muted">Frps 运行端口</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group mb-0">
                                        <label for="node_adminport" class="font-weight-bold">管理端口</label>
                                        <input type="number" class="form-control" id="node_adminport" placeholder="7500">
                                        <small class="form-text text-muted">管理 API 端口</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 认证配置 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <i class="fas fa-key mr-1"></i>认证配置
                            </div>
                            <div class="form-group mb-3">
                                <label for="node_adminpass" class="font-weight-bold">管理密码</label>
                                <input type="text" class="form-control" id="node_adminpass" placeholder="管理API密码">
                                <small class="form-text text-muted">用于系统接口认证</small>
                            </div>
                            <div class="form-group mb-0">
                                <label for="node_token" class="font-weight-bold">连接 Token</label>
                                <input type="text" class="form-control" id="node_token" placeholder="客户端连接令牌">
                                <small class="form-text text-muted">用于 Frpc 客户端连接</small>
                            </div>
                        </div>

                        <!-- 权限配置 -->
                        <div class="form-section">
                            <div class="form-section-title">
                                <i class="fas fa-users mr-1"></i>权限配置
                            </div>
                            <div class="form-group mb-3">
                                <label for="node_group" class="font-weight-bold">允许的用户组</label>
                                <input type="text" class="form-control" id="node_group" placeholder="default;vip1;vip2;" value="default;">
                                <small class="form-text text-muted">用分号(;)分隔多个用户组名</small>
                            </div>
                            <div class="form-group mb-0">
                                <label for="node_status" class="font-weight-bold">节点状态</label>
                                <select class="form-control" id="node_status">
                                    <option value="200">正常 - 允许用户使用，系统自动检测</option>
                                    <option value="401">隐藏 - 对用户隐藏，管理员可见</option>
                                    <option value="403">禁用 - 禁止用户使用此节点</option>
                                    <option value="500">离线 - 标记为离线状态</option>
                                </select>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    系统会自动检测连接状态，手动设置的隐藏和禁用状态不会被覆盖
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                <i class="fas fa-undo mr-1"></i>重置
                            </button>
                            <button type="button" class="btn btn-primary" onclick="save()">
                                <i class="fas fa-save mr-1"></i>保存节点
                            </button>
                        </div>
                    </div>
                </div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
var csrf_token = "<?php echo $_SESSION['token']; ?>";
var nodeid = undefined;
function search() {
	window.location = window.location.href + '&search=' + encodeURIComponent($(searchdata).val());
}
function edit(id) {
	var htmlobj = $.ajax({
		type: 'GET',
		url: "?page=panel&module=nodes&getinfo=" + id + "&csrf=" + csrf_token,
		async:true,
		error: function() {
			showAlert("错误：" + htmlobj.responseText, 'danger');
			return;
		},
		success: function() {
			try {
				var json = JSON.parse(htmlobj.responseText);
				nodeid = id;
				$("#node_name").val(json.name);
				$("#node_description").val(json.description);
				$("#node_hostname").val(json.hostname);
				$("#node_ip").val(json.ip);
				$("#node_port").val(json.port);
				$("#node_adminport").val(json.admin_port);
				$("#node_adminpass").val(json.admin_pass);
				$("#node_token").val(json.token);
				$("#node_group").val(json.group);
				$("#node_status").val(json.status);

				// 更新界面状态
				$("#form-title").html('<i class="fas fa-edit mr-2"></i>编辑节点');
				$("#statusmsg").removeClass('alert-info alert-success alert-danger')
							  .addClass('alert-info')
							  .html('<i class="fas fa-edit mr-2"></i>正在编辑节点: <strong>' + json.name + '</strong>');

				// 滚动到表单
				$('html, body').animate({
					scrollTop: $("#form-title").offset().top - 100
				}, 500);
			} catch(e) {
				showAlert("错误：无法解析服务器返回的数据", 'danger');
			}
			return;
		}
	});
}
function deletenode(id) {
	if(!confirm("你确定要删除这个节点吗？此操作不可恢复！\n\n该节点下所有的隧道也会被删除！")) {
		return;
	}
	var htmlobj = $.ajax({
		type: 'POST',
		url: "?action=deletenode&page=panel&module=nodes&csrf=" + csrf_token,
		async:true,
		data: {
			id: id
		},
		error: function() {
			alert("错误：" + htmlobj.responseText);
			return;
		},
		success: function() {
			alert(htmlobj.responseText);
			window.location.reload();
			return;
		}
	});
}
function save() {
	// 表单验证
	if (!validateForm()) {
		return;
	}

	var url = "?action=updatenode&page=panel&module=nodes";
	var isEdit = (nodeid !== undefined);

	if (!isEdit) {
		nodeid = null;
		url = "?action=addnode&page=panel&module=nodes";
	}

	// 显示保存中状态
	var saveBtn = $("button[onclick='save()']");
	var originalText = saveBtn.html();
	saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>保存中...');

	var htmlobj = $.ajax({
		type: 'POST',
		url: url + "&csrf=" + csrf_token,
		async: true,
		data: {
			id: nodeid,
			name: $("#node_name").val().trim(),
			description: $("#node_description").val().trim(),
			hostname: $("#node_hostname").val().trim(),
			ip: $("#node_ip").val().trim(),
			port: $("#node_port").val(),
			admin_port: $("#node_adminport").val(),
			admin_pass: $("#node_adminpass").val(),
			token: $("#node_token").val().trim(),
			group: $("#node_group").val().trim(),
			status: $("#node_status").val()
		},
		error: function() {
			showAlert("保存失败：" + htmlobj.responseText, 'danger');
			saveBtn.prop('disabled', false).html(originalText);
		},
		success: function() {
			showAlert(htmlobj.responseText, 'success');

			// 延迟刷新页面，让用户看到成功消息
			setTimeout(function() {
				window.location.reload();
			}, 1500);
		}
	});
}

// 显示提示信息
function showAlert(message, type = 'info') {
	$("#statusmsg").removeClass('alert-info alert-success alert-danger alert-warning')
				  .addClass('alert-' + type)
				  .html('<i class="fas fa-' + getAlertIcon(type) + ' mr-2"></i>' + message);
}

function getAlertIcon(type) {
	const icons = {
		'info': 'info-circle',
		'success': 'check-circle',
		'danger': 'exclamation-triangle',
		'warning': 'exclamation-circle'
	};
	return icons[type] || 'info-circle';
}

// 重置表单
function resetForm() {
	nodeid = undefined;
	$("#form-title").html('<i class="fas fa-plus-circle mr-2"></i>添加节点');
	$("#statusmsg").removeClass('alert-info alert-success alert-danger alert-warning')
				  .addClass('alert-info')
				  .html('<i class="fas fa-info-circle mr-2"></i>填写下方表单来添加新的服务器节点');

	// 清空所有表单字段
	$("#node_name, #node_description, #node_hostname, #node_ip, #node_port, #node_adminport, #node_adminpass, #node_token").val('');
	$("#node_group").val('default;');
	$("#node_status").val('200');
}

// 检测单个节点状态
function checkNodeStatus(nodeId) {
	// 显示检测中状态
	var statusElement = $("#status-" + nodeId);
	var responseElement = $("#response-time-" + nodeId);

	statusElement.removeClass().addClass('badge node-status-badge')
				 .html('<i class="fas fa-spinner fa-spin"></i> 检测中');
	responseElement.html('-');

	$.ajax({
		type: 'POST',
		url: "?action=checknodestatus&page=panel&module=nodes&csrf=" + csrf_token,
		data: { id: nodeId },
		dataType: 'json',
		success: function(result) {
			updateNodeStatusDisplay(nodeId, result);
		},
		error: function(xhr) {
			statusElement.removeClass().addClass('badge node-status-badge status-offline')
						 .html('检测失败');
			responseElement.html('-');
			console.error('节点状态检测失败:', xhr.responseText);
		}
	});
}

// 检测所有节点状态
function checkAllNodesStatus() {
	// 禁用按钮并显示检测中状态
	var checkBtn = $('#check-all-btn');
	checkBtn.prop('disabled', true);
	checkBtn.html('<i class="fas fa-spinner fa-spin"></i> 检测中...');

	// 显示所有节点为检测中状态
	$('[id^="status-"]').html('<i class="fas fa-spinner fa-spin"></i> 检测中...');
	$('[id^="response-time-"]').html('-');

	$.ajax({
		type: 'POST',
		url: "?action=checkallnodesstatus&page=panel&module=nodes&csrf=" + csrf_token,
		dataType: 'json',
		success: function(results) {
			for (var nodeId in results) {
				updateNodeStatusDisplay(nodeId, results[nodeId]);
			}
			// 更新最后检测时间
			updateLastCheckTime();
		},
		error: function(xhr) {
			$('[id^="status-"]').html('<span class="text-danger">检测失败</span>');
			$('[id^="response-time-"]').html('-');
			alert('批量检测失败: ' + xhr.responseText);
		},
		complete: function() {
			// 恢复按钮状态
			checkBtn.prop('disabled', false);
			checkBtn.html('<i class="fas fa-sync-alt"></i> 检测所有节点');
		}
	});
}

// 更新节点状态显示
function updateNodeStatusDisplay(nodeId, result) {
	var statusElement = $("#status-" + nodeId);
	var responseTimeElement = $("#response-time-" + nodeId);

	// 状态配置映射
	var statusConfig = {
		'online': { text: '正常', badgeClass: 'status-online' },
		'offline': { text: '离线', badgeClass: 'status-offline' },
		'disabled': { text: '禁用', badgeClass: 'status-disabled' },
		'hidden': { text: '隐藏', badgeClass: 'status-hidden' },
		'error': { text: '异常', badgeClass: 'status-offline' }
	};

	var config = statusConfig[result.status] || { text: '未知', badgeClass: 'status-offline' };

	// 更新状态显示
	statusElement.removeClass().addClass('badge node-status-badge ' + config.badgeClass);
	statusElement.html(config.text);

	// 更新响应时间
	if (result.response_time > 0) {
		var timeClass = 'response-good';
		if (result.response_time >= 500) {
			timeClass = 'response-poor';
		} else if (result.response_time >= 100) {
			timeClass = 'response-medium';
		}

		responseTimeElement.removeClass().addClass('response-time ' + timeClass);
		responseTimeElement.html(result.response_time + 'ms');
	} else {
		responseTimeElement.removeClass().addClass('response-time text-muted');
		responseTimeElement.html('-');
	}

	// 添加提示信息
	if (result.message) {
		statusElement.attr('title', result.message);
		responseTimeElement.attr('title', result.message);
	}

	// 更新统计卡片
	updateStatsCards();
}

// 更新最后检测时间
function updateLastCheckTime() {
	var now = new Date();
	var timeString = now.getFullYear() + '-' +
					 String(now.getMonth() + 1).padStart(2, '0') + '-' +
					 String(now.getDate()).padStart(2, '0') + ' ' +
					 String(now.getHours()).padStart(2, '0') + ':' +
					 String(now.getMinutes()).padStart(2, '0') + ':' +
					 String(now.getSeconds()).padStart(2, '0');
	var element = $('#last-check-time');
	element.html('最后检测: ' + timeString);
	element.data('timestamp', now.getTime());
}

// 格式化时间差
function getTimeAgo(timestamp) {
	var now = new Date().getTime();
	var diff = now - timestamp;
	var seconds = Math.floor(diff / 1000);
	var minutes = Math.floor(seconds / 60);
	var hours = Math.floor(minutes / 60);

	if (hours > 0) {
		return hours + '小时前';
	} else if (minutes > 0) {
		return minutes + '分钟前';
	} else {
		return seconds + '秒前';
	}
}

// 更新统计卡片
function updateStatsCards() {
	var totalNodes = $('.node-row').length;
	var onlineNodes = $('.status-online').length;
	var offlineNodes = $('.status-offline').length;
	var disabledNodes = $('.status-disabled').length;

	// 更新统计数字（如果需要实时更新）
	// 这里可以添加动画效果
}

// 表单验证
function validateForm() {
	var name = $("#node_name").val().trim();
	var hostname = $("#node_hostname").val().trim();
	var ip = $("#node_ip").val().trim();
	var port = $("#node_port").val();

	if (!name) {
		showAlert("请输入节点名称", 'warning');
		$("#node_name").focus();
		return false;
	}

	if (!hostname) {
		showAlert("请输入主机名称", 'warning');
		$("#node_hostname").focus();
		return false;
	}

	if (!ip) {
		showAlert("请输入IP地址", 'warning');
		$("#node_ip").focus();
		return false;
	}

	if (!port || port < 1 || port > 65535) {
		showAlert("请输入有效的端口号 (1-65535)", 'warning');
		$("#node_port").focus();
		return false;
	}

	// IP地址格式验证
	var ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
	if (!ipRegex.test(ip)) {
		showAlert("请输入有效的IP地址格式", 'warning');
		$("#node_ip").focus();
		return false;
	}

	return true;
}

// 页面加载完成后自动检测所有节点状态
$(document).ready(function() {
	// 延迟1秒后自动检测，避免页面加载时的阻塞
	setTimeout(function() {
		checkAllNodesStatus();
	}, 1000);

	// 每5分钟自动检测一次
	setInterval(function() {
		checkAllNodesStatus();
	}, 300000); // 300000ms = 5分钟

	// 每30秒更新一次"时间前"显示
	setInterval(function() {
		var lastCheckElement = $('#last-check-time');
		var lastCheckTime = lastCheckElement.data('timestamp');
		if (lastCheckTime) {
			lastCheckElement.html('最后检测: ' + getTimeAgo(lastCheckTime));
		}
	}, 30000);

	// 表单字段实时验证
	$("#node_name, #node_hostname, #node_ip, #node_port").on('blur', function() {
		$(this).removeClass('is-invalid');
	});
});
</script>
